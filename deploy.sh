#!/bin/bash

# CV Builder SaaS Deployment Script
# This script deploys the application to production

set -e

echo "🚀 Starting CV Builder SaaS Deployment..."

# Configuration
DOMAIN=${1:-"your-domain.com"}
ENV=${2:-"production"}

echo "📋 Deployment Configuration:"
echo "   Domain: $DOMAIN"
echo "   Environment: $ENV"
echo ""

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p nginx/ssl
mkdir -p database/init
mkdir -p logs

# Generate Laravel application key if not exists
if [ ! -f backend/.env ]; then
    echo "🔑 Setting up Laravel environment..."
    cp backend/.env.production backend/.env
    
    # Generate application key
    cd backend
    php artisan key:generate --force
    cd ..
fi

# Build and start containers
echo "🐳 Building Docker containers..."
docker-compose down --remove-orphans
docker-compose build --no-cache

echo "🚀 Starting services..."
docker-compose up -d

# Wait for database to be ready
echo "⏳ Waiting for database to be ready..."
sleep 30

# Run database migrations
echo "🗄️ Running database migrations..."
docker-compose exec backend php artisan migrate --force

# Seed database if needed
echo "🌱 Seeding database..."
docker-compose exec backend php artisan db:seed --force

# Clear and cache configuration
echo "🧹 Optimizing application..."
docker-compose exec backend php artisan config:cache
docker-compose exec backend php artisan route:cache
docker-compose exec backend php artisan view:cache

# Set proper permissions
echo "🔐 Setting permissions..."
docker-compose exec backend chown -R www-data:www-data /var/www/html/storage
docker-compose exec backend chown -R www-data:www-data /var/www/html/bootstrap/cache

# Health check
echo "🏥 Running health checks..."
sleep 10

# Check backend health
if curl -f http://localhost:8000/api/health > /dev/null 2>&1; then
    echo "✅ Backend is healthy"
else
    echo "❌ Backend health check failed"
fi

# Check frontend health
if curl -f http://localhost:3000 > /dev/null 2>&1; then
    echo "✅ Frontend is healthy"
else
    echo "❌ Frontend health check failed"
fi

echo ""
echo "🎉 Deployment completed!"
echo ""
echo "📊 Application Status:"
echo "   Frontend: http://localhost:3000"
echo "   Backend API: http://localhost:8000"
echo "   Database: localhost:3306"
echo ""
echo "📝 Next Steps:"
echo "   1. Configure your domain DNS to point to this server"
echo "   2. Set up SSL certificates (Let's Encrypt recommended)"
echo "   3. Configure Stripe webhooks in your Stripe dashboard"
echo "   4. Set up monitoring and backups"
echo ""
echo "🔧 Useful Commands:"
echo "   View logs: docker-compose logs -f"
echo "   Stop services: docker-compose down"
echo "   Restart services: docker-compose restart"
echo "   Update application: git pull && ./deploy.sh $DOMAIN $ENV"
