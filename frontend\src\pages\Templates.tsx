import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Layout } from '../components/Layout';
import { templateService } from '../services/templateService';
import { Template } from '../types';

export const Templates: React.FC = () => {
  const [templates, setTemplates] = useState<Template[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchTemplates = async () => {
      try {
        const response = await templateService.getTemplates();
        setTemplates(response.templates);
      } catch (error) {
        console.error('Error fetching templates:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchTemplates();
  }, []);

  return (
    <Layout>
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">CV Templates</h1>
          <p className="text-gray-600">Choose a template to start building your CV</p>
        </div>

        {loading ? (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {templates.map((template) => (
              <div key={template.id} className="card hover:shadow-lg transition-shadow">
                <div className="aspect-w-3 aspect-h-4 mb-4">
                  <div className="bg-gray-100 rounded-lg flex items-center justify-center">
                    {template.preview_image ? (
                      <img
                        src={template.preview_image}
                        alt={template.display_name}
                        className="w-full h-48 object-cover rounded-lg"
                      />
                    ) : (
                      <div className="text-gray-400 text-4xl">📄</div>
                    )}
                  </div>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {template.display_name}
                </h3>
                <p className="text-gray-600 text-sm mb-4">
                  {template.description || 'A professional CV template'}
                </p>
                <Link
                  to={`/cv-builder?template=${template.id}`}
                  className="btn-primary w-full text-center"
                >
                  Use This Template
                </Link>
              </div>
            ))}
          </div>
        )}
      </div>
    </Layout>
  );
};
