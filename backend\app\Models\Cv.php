<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Cv extends Model
{
    protected $fillable = [
        'user_id',
        'template_id',
        'title',
        'personal_info',
        'education',
        'experience',
        'skills',
        'languages',
        'additional_sections',
        'is_draft',
        'last_edited_at',
    ];

    protected $casts = [
        'personal_info' => 'array',
        'education' => 'array',
        'experience' => 'array',
        'skills' => 'array',
        'languages' => 'array',
        'additional_sections' => 'array',
        'is_draft' => 'boolean',
        'last_edited_at' => 'datetime',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function template(): BelongsTo
    {
        return $this->belongsTo(Template::class);
    }
}
