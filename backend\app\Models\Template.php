<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Template extends Model
{
    protected $fillable = [
        'name',
        'display_name',
        'description',
        'preview_image',
        'styles',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'styles' => 'array',
        'is_active' => 'boolean',
    ];

    public function cvs(): HasMany
    {
        return $this->hasMany(Cv::class);
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }
}
