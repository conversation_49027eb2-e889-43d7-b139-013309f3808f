<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class TemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $templates = [
            [
                'name' => 'modern',
                'display_name' => 'Modern Template',
                'description' => 'A clean, modern design perfect for tech professionals and creative roles.',
                'preview_image' => 'templates/modern-preview.jpg',
                'styles' => json_encode([
                    'primary_color' => '#3b82f6',
                    'secondary_color' => '#64748b',
                    'font_family' => 'Inter, sans-serif',
                    'layout' => 'single-column'
                ]),
                'is_active' => true,
                'sort_order' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'classic',
                'display_name' => 'Classic Template',
                'description' => 'A traditional, professional layout suitable for corporate and formal positions.',
                'preview_image' => 'templates/classic-preview.jpg',
                'styles' => json_encode([
                    'primary_color' => '#1f2937',
                    'secondary_color' => '#6b7280',
                    'font_family' => 'Times New Roman, serif',
                    'layout' => 'two-column'
                ]),
                'is_active' => true,
                'sort_order' => 2,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'creative',
                'display_name' => 'Creative Template',
                'description' => 'A bold, creative design ideal for designers, artists, and marketing professionals.',
                'preview_image' => 'templates/creative-preview.jpg',
                'styles' => json_encode([
                    'primary_color' => '#7c3aed',
                    'secondary_color' => '#a78bfa',
                    'font_family' => 'Poppins, sans-serif',
                    'layout' => 'creative-grid'
                ]),
                'is_active' => true,
                'sort_order' => 3,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        DB::table('templates')->insert($templates);
    }
}
