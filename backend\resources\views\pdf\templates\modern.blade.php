<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $personal_info['name'] ?? 'CV' }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #3b82f6;
        }
        
        .name {
            font-size: 28px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 5px;
        }
        
        .contact-info {
            font-size: 11px;
            color: #6b7280;
        }
        
        .section {
            margin-bottom: 25px;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #3b82f6;
            margin-bottom: 10px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .experience-item, .education-item {
            margin-bottom: 15px;
            padding-left: 15px;
            border-left: 3px solid #e5e7eb;
        }
        
        .item-title {
            font-weight: 600;
            color: #1f2937;
        }
        
        .item-company, .item-school {
            color: #3b82f6;
            font-weight: 500;
        }
        
        .item-date {
            color: #6b7280;
            font-size: 10px;
            font-style: italic;
        }
        
        .item-description {
            margin-top: 5px;
            color: #4b5563;
        }
        
        .skills-grid {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .skill-item {
            background: #f3f4f6;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 10px;
            color: #374151;
        }
        
        .languages-list {
            list-style: none;
        }
        
        .language-item {
            margin-bottom: 5px;
            color: #4b5563;
        }
        
        .language-name {
            font-weight: 500;
            color: #1f2937;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <h1 class="name">{{ $personal_info['name'] ?? 'Your Name' }}</h1>
            <div class="contact-info">
                @if(isset($personal_info['email']))
                    {{ $personal_info['email'] }}
                @endif
                @if(isset($personal_info['phone']))
                    | {{ $personal_info['phone'] }}
                @endif
                @if(isset($personal_info['address']))
                    | {{ $personal_info['address'] }}
                @endif
            </div>
        </div>

        <!-- Professional Summary -->
        @if(isset($personal_info['summary']))
        <div class="section">
            <h2 class="section-title">Professional Summary</h2>
            <p>{{ $personal_info['summary'] }}</p>
        </div>
        @endif

        <!-- Experience -->
        @if(!empty($experience))
        <div class="section">
            <h2 class="section-title">Professional Experience</h2>
            @foreach($experience as $exp)
            <div class="experience-item">
                <div class="item-title">{{ $exp['position'] ?? 'Position' }}</div>
                <div class="item-company">{{ $exp['company'] ?? 'Company' }}</div>
                <div class="item-date">
                    {{ $exp['start_date'] ?? 'Start' }} - {{ $exp['end_date'] ?? 'End' }}
                </div>
                @if(isset($exp['description']))
                <div class="item-description">{{ $exp['description'] }}</div>
                @endif
            </div>
            @endforeach
        </div>
        @endif

        <!-- Education -->
        @if(!empty($education))
        <div class="section">
            <h2 class="section-title">Education</h2>
            @foreach($education as $edu)
            <div class="education-item">
                <div class="item-title">{{ $edu['degree'] ?? 'Degree' }}</div>
                <div class="item-school">{{ $edu['school'] ?? 'School' }}</div>
                <div class="item-date">
                    {{ $edu['start_date'] ?? 'Start' }} - {{ $edu['end_date'] ?? 'End' }}
                </div>
                @if(isset($edu['description']))
                <div class="item-description">{{ $edu['description'] }}</div>
                @endif
            </div>
            @endforeach
        </div>
        @endif

        <!-- Skills -->
        @if(!empty($skills))
        <div class="section">
            <h2 class="section-title">Skills</h2>
            <div class="skills-grid">
                @foreach($skills as $skill)
                <span class="skill-item">{{ is_array($skill) ? $skill['name'] : $skill }}</span>
                @endforeach
            </div>
        </div>
        @endif

        <!-- Languages -->
        @if(!empty($languages))
        <div class="section">
            <h2 class="section-title">Languages</h2>
            <ul class="languages-list">
                @foreach($languages as $lang)
                <li class="language-item">
                    <span class="language-name">{{ is_array($lang) ? $lang['name'] : $lang }}</span>
                    @if(is_array($lang) && isset($lang['level']))
                    - {{ $lang['level'] }}
                    @endif
                </li>
                @endforeach
            </ul>
        </div>
        @endif
    </div>
</body>
</html>
