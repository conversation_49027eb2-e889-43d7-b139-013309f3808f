<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Subscription;
use App\Services\StripeService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class SubscriptionController extends Controller
{
    /**
     * Get user's current subscription.
     */
    public function show(Request $request): JsonResponse
    {
        $subscription = $request->user()->subscription;

        if (!$subscription) {
            // Create default free subscription if none exists
            $subscription = $request->user()->subscription()->create([
                'plan_type' => 'free',
                'status' => 'active',
                'cv_limit' => 1,
            ]);
        }

        return response()->json([
            'subscription' => $subscription
        ]);
    }

    /**
     * Get available subscription plans.
     */
    public function plans(): JsonResponse
    {
        $stripeService = new StripeService();
        $plans = $stripeService->getPlans();

        return response()->json([
            'plans' => $plans
        ]);
    }

    /**
     * Create Stripe checkout session (placeholder for Stripe integration).
     */
    public function createCheckoutSession(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'plan_id' => 'required|string|in:premium,enterprise',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $stripeService = new StripeService();
        $user = $request->user();

        // Get the price ID for the plan
        $priceIds = [
            'premium' => 'price_premium_monthly',
            'enterprise' => 'price_enterprise_monthly',
        ];

        $priceId = $priceIds[$request->plan_id];

        $successUrl = url('/dashboard?payment=success');
        $cancelUrl = url('/pricing?payment=cancelled');

        $result = $stripeService->createCheckoutSession($user, $priceId, $successUrl, $cancelUrl);

        if ($result['success']) {
            return response()->json([
                'success' => true,
                'checkout_url' => $result['checkout_url'],
                'session_id' => $result['session_id']
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => $result['error']
            ], 400);
        }
    }

    /**
     * Handle Stripe webhook.
     */
    public function webhook(Request $request): JsonResponse
    {
        $stripeService = new StripeService();
        $result = $stripeService->handleWebhook($request->all());

        if ($result['success']) {
            return response()->json(['message' => 'Webhook processed successfully']);
        } else {
            return response()->json([
                'message' => 'Webhook processing failed',
                'error' => $result['error']
            ], 400);
        }
    }
}
