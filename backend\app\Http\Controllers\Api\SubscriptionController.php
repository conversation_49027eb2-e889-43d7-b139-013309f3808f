<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Subscription;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class SubscriptionController extends Controller
{
    /**
     * Get user's current subscription.
     */
    public function show(Request $request): JsonResponse
    {
        $subscription = $request->user()->subscription;

        if (!$subscription) {
            // Create default free subscription if none exists
            $subscription = $request->user()->subscription()->create([
                'plan_type' => 'free',
                'status' => 'active',
                'cv_limit' => 1,
            ]);
        }

        return response()->json([
            'subscription' => $subscription
        ]);
    }

    /**
     * Get available subscription plans.
     */
    public function plans(): JsonResponse
    {
        $plans = [
            [
                'id' => 'free',
                'name' => 'Free Plan',
                'price' => 0,
                'cv_limit' => 1,
                'features' => [
                    '1 CV',
                    'Basic templates',
                    'PDF download'
                ]
            ],
            [
                'id' => 'premium',
                'name' => 'Premium Plan',
                'price' => 9.99,
                'cv_limit' => -1, // unlimited
                'features' => [
                    'Unlimited CVs',
                    'All templates',
                    'PDF download',
                    'Priority support'
                ]
            ]
        ];

        return response()->json([
            'plans' => $plans
        ]);
    }

    /**
     * Create Stripe checkout session (placeholder for Stripe integration).
     */
    public function createCheckoutSession(Request $request): JsonResponse
    {
        // This would integrate with Stripe in a real implementation
        // For now, we'll return a placeholder response

        return response()->json([
            'message' => 'Stripe integration not implemented yet',
            'checkout_url' => 'https://checkout.stripe.com/placeholder'
        ]);
    }

    /**
     * Handle Stripe webhook (placeholder).
     */
    public function webhook(Request $request): JsonResponse
    {
        // This would handle Stripe webhooks in a real implementation

        return response()->json([
            'message' => 'Webhook received'
        ]);
    }
}
