<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cvs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('template_id')->constrained()->onDelete('cascade');
            $table->string('title');
            $table->json('personal_info'); // name, email, phone, address, etc.
            $table->json('education')->nullable(); // array of education entries
            $table->json('experience')->nullable(); // array of work experience entries
            $table->json('skills')->nullable(); // array of skills
            $table->json('languages')->nullable(); // array of languages
            $table->json('additional_sections')->nullable(); // any additional custom sections
            $table->boolean('is_draft')->default(true);
            $table->timestamp('last_edited_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cvs');
    }
};
