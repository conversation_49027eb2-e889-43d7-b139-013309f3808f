<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Subscription extends Model
{
    protected $fillable = [
        'user_id',
        'stripe_subscription_id',
        'stripe_customer_id',
        'plan_type',
        'status',
        'amount',
        'current_period_start',
        'current_period_end',
        'cancelled_at',
        'cv_limit',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'current_period_start' => 'datetime',
        'current_period_end' => 'datetime',
        'cancelled_at' => 'datetime',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function isActive(): bool
    {
        return $this->status === 'active' &&
               $this->current_period_end &&
               $this->current_period_end->isFuture();
    }

    public function isPremium(): bool
    {
        return $this->plan_type === 'premium' && $this->isActive();
    }
}
