[2025-07-12 14:14:33] local.ERROR: Class "finfo" not found {"exception":"[object] (Error(code: 0): Class \"finfo\" not found at C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\league\\mime-type-detection\\src\\FinfoMimeTypeDetector.php:48)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\league\\flysystem-local\\LocalFilesystemAdapter.php(86): League\\MimeTypeDetection\\FinfoMimeTypeDetector->__construct()
#1 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\VendorPublishCommand.php(295): League\\Flysystem\\Local\\LocalFilesystemAdapter->__construct()
#2 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\VendorPublishCommand.php(244): Illuminate\\Foundation\\Console\\VendorPublishCommand->publishDirectory()
#3 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\VendorPublishCommand.php(207): Illuminate\\Foundation\\Console\\VendorPublishCommand->publishItem()
#4 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\VendorPublishCommand.php(102): Illuminate\\Foundation\\Console\\VendorPublishCommand->publishTag()
#5 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\VendorPublishCommand->handle()
#6 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#7 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#8 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#9 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#10 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#11 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#12 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#13 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run()
#14 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#15 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#16 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#17 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#18 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#19 {main}
"} 
[2025-07-12 14:40:07] local.ERROR: Command "make:service" is not defined.

Did you mean one of these?
    make:cache-table
    make:cast
    make:channel
    make:class
    make:command
    make:component
    make:controller
    make:enum
    make:event
    make:exception
    make:factory
    make:interface
    make:job
    make:job-middleware
    make:listener
    make:mail
    make:middleware
    make:migration
    make:model
    make:notification
    make:notifications-table
    make:observer
    make:policy
    make:provider
    make:queue-batches-table
    make:queue-failed-table
    make:queue-table
    make:request
    make:resource
    make:rule
    make:scope
    make:seeder
    make:session-table
    make:test
    make:trait
    make:view {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"make:service\" is not defined.

Did you mean one of these?
    make:cache-table
    make:cast
    make:channel
    make:class
    make:command
    make:component
    make:controller
    make:enum
    make:event
    make:exception
    make:factory
    make:interface
    make:job
    make:job-middleware
    make:listener
    make:mail
    make:middleware
    make:migration
    make:model
    make:notification
    make:notifications-table
    make:observer
    make:policy
    make:provider
    make:queue-batches-table
    make:queue-failed-table
    make:queue-table
    make:request
    make:resource
    make:rule
    make:scope
    make:seeder
    make:session-table
    make:test
    make:trait
    make:view at C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\symfony\\console\\Application.php:725)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\symfony\\console\\Application.php(283): Symfony\\Component\\Console\\Application->find()
#1 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#2 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#3 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#4 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#5 {main}
"} 
[2025-07-12 15:18:01] local.ERROR: SQLSTATE[HY000]: General error: 1 no such table: personal_access_tokens (Connection: sqlite, SQL: insert into "personal_access_tokens" ("name", "token", "abilities", "expires_at", "tokenable_id", "tokenable_type", "updated_at", "created_at") values (auth_token, 92744ebd02e7fe1e03ba6425c1ae23faeebeea43b665f48501c30b85c957d55f, ["*"], ?, 2, App\Models\User, 2025-07-12 15:18:01, 2025-07-12 15:18:01)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 no such table: personal_access_tokens (Connection: sqlite, SQL: insert into \"personal_access_tokens\" (\"name\", \"token\", \"abilities\", \"expires_at\", \"tokenable_id\", \"tokenable_type\", \"updated_at\", \"created_at\") values (auth_token, 92744ebd02e7fe1e03ba6425c1ae23faeebeea43b665f48501c30b85c957d55f, [\"*\"], ?, 2, App\\Models\\User, 2025-07-12 15:18:01, 2025-07-12 15:18:01)) at C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#1 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#2 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(521): Illuminate\\Database\\Connection->statement()
#3 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert()
#4 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId()
#5 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId()
#6 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1431): Illuminate\\Database\\Eloquent\\Builder->__call()
#7 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1396): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#8 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1235): Illuminate\\Database\\Eloquent\\Model->performInsert()
#9 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(370): Illuminate\\Database\\Eloquent\\Model->save()
#10 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->{closure:Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany::create():367}()
#11 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(367): tap()
#12 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\sanctum\\src\\HasApiTokens.php(64): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->create()
#13 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\app\\Http\\Controllers\\Api\\AuthController.php(46): App\\Models\\User->createToken()
#14 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Api\\AuthController->register()
#15 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#16 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#17 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#18 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}()
#19 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}()
#20 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#21 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#22 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#23 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#24 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#25 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#26 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#27 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}()
#28 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}()
#29 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#30 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#31 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#32 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#33 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#34 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#35 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#36 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#37 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#38 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#39 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#40 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#41 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#42 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#43 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#44 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#45 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#46 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#47 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#48 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#49 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#50 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#51 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#52 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 no such table: personal_access_tokens at C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:562)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(562): PDO->prepare()
#1 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->{closure:Illuminate\\Database\\Connection::statement():557}()
#2 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback()
#3 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run()
#4 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(521): Illuminate\\Database\\Connection->statement()
#5 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert()
#6 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId()
#7 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId()
#8 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1431): Illuminate\\Database\\Eloquent\\Builder->__call()
#9 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1396): Illuminate\\Database\\Eloquent\\Model->insertAndSetId()
#10 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1235): Illuminate\\Database\\Eloquent\\Model->performInsert()
#11 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(370): Illuminate\\Database\\Eloquent\\Model->save()
#12 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->{closure:Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany::create():367}()
#13 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany.php(367): tap()
#14 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\sanctum\\src\\HasApiTokens.php(64): Illuminate\\Database\\Eloquent\\Relations\\HasOneOrMany->create()
#15 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\app\\Http\\Controllers\\Api\\AuthController.php(46): App\\Models\\User->createToken()
#16 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Api\\AuthController->register()
#17 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#18 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#19 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#20 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->{closure:Illuminate\\Routing\\Router::runRouteWithinStack():807}()
#21 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}()
#22 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#23 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#24 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#25 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#26 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute()
#27 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#28 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch()
#29 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->{closure:Illuminate\\Foundation\\Http\\Kernel::dispatchToRouter():197}()
#30 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:Illuminate\\Pipeline\\Pipeline::prepareDestination():167}()
#31 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#32 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#33 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#34 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#35 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#36 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#37 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#38 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#39 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#40 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(61): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#41 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle()
#42 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#43 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#44 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#45 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#46 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#47 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle()
#48 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->{closure:{closure:Illuminate\\Pipeline\\Pipeline::carry():183}:184}()
#49 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then()
#50 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#51 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle()
#52 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest()
#53 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('...')
#54 {main}
"} 
[2025-07-12 15:18:31] local.ERROR: Class "finfo" not found {"exception":"[object] (Error(code: 0): Class \"finfo\" not found at C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\league\\mime-type-detection\\src\\FinfoMimeTypeDetector.php:48)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\league\\flysystem-local\\LocalFilesystemAdapter.php(86): League\\MimeTypeDetection\\FinfoMimeTypeDetector->__construct()
#1 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\VendorPublishCommand.php(295): League\\Flysystem\\Local\\LocalFilesystemAdapter->__construct()
#2 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\VendorPublishCommand.php(244): Illuminate\\Foundation\\Console\\VendorPublishCommand->publishDirectory()
#3 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\VendorPublishCommand.php(207): Illuminate\\Foundation\\Console\\VendorPublishCommand->publishItem()
#4 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\VendorPublishCommand.php(102): Illuminate\\Foundation\\Console\\VendorPublishCommand->publishTag()
#5 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\VendorPublishCommand->handle()
#6 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#7 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#8 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#9 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#10 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#11 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#12 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#13 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run()
#14 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#15 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#16 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#17 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#18 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#19 {main}
"} 
