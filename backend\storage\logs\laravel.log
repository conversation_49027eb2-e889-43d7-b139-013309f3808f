[2025-07-12 14:14:33] local.ERROR: Class "finfo" not found {"exception":"[object] (Error(code: 0): Class \"finfo\" not found at C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\league\\mime-type-detection\\src\\FinfoMimeTypeDetector.php:48)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\league\\flysystem-local\\LocalFilesystemAdapter.php(86): League\\MimeTypeDetection\\FinfoMimeTypeDetector->__construct()
#1 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\VendorPublishCommand.php(295): League\\Flysystem\\Local\\LocalFilesystemAdapter->__construct()
#2 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\VendorPublishCommand.php(244): Illuminate\\Foundation\\Console\\VendorPublishCommand->publishDirectory()
#3 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\VendorPublishCommand.php(207): Illuminate\\Foundation\\Console\\VendorPublishCommand->publishItem()
#4 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\VendorPublishCommand.php(102): Illuminate\\Foundation\\Console\\VendorPublishCommand->publishTag()
#5 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Foundation\\Console\\VendorPublishCommand->handle()
#6 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::{closure:Illuminate\\Container\\BoundMethod::call():35}()
#7 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure()
#8 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod()
#9 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call()
#10 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call()
#11 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute()
#12 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run()
#13 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run()
#14 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand()
#15 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#16 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#17 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#18 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#19 {main}
"} 
[2025-07-12 14:40:07] local.ERROR: Command "make:service" is not defined.

Did you mean one of these?
    make:cache-table
    make:cast
    make:channel
    make:class
    make:command
    make:component
    make:controller
    make:enum
    make:event
    make:exception
    make:factory
    make:interface
    make:job
    make:job-middleware
    make:listener
    make:mail
    make:middleware
    make:migration
    make:model
    make:notification
    make:notifications-table
    make:observer
    make:policy
    make:provider
    make:queue-batches-table
    make:queue-failed-table
    make:queue-table
    make:request
    make:resource
    make:rule
    make:scope
    make:seeder
    make:session-table
    make:test
    make:trait
    make:view {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"make:service\" is not defined.

Did you mean one of these?
    make:cache-table
    make:cast
    make:channel
    make:class
    make:command
    make:component
    make:controller
    make:enum
    make:event
    make:exception
    make:factory
    make:interface
    make:job
    make:job-middleware
    make:listener
    make:mail
    make:middleware
    make:migration
    make:model
    make:notification
    make:notifications-table
    make:observer
    make:policy
    make:provider
    make:queue-batches-table
    make:queue-failed-table
    make:queue-table
    make:request
    make:resource
    make:rule
    make:scope
    make:seeder
    make:session-table
    make:test
    make:trait
    make:view at C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\symfony\\console\\Application.php:725)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\symfony\\console\\Application.php(283): Symfony\\Component\\Console\\Application->find()
#1 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun()
#2 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run()
#3 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle()
#4 C:\\Users\\<USER>\\Desktop\\Projects\\CV SAAS\\backend\\artisan(16): Illuminate\\Foundation\\Application->handleCommand()
#5 {main}
"} 
