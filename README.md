# CV Builder SaaS Platform

A comprehensive web-based SaaS platform for building, previewing, and downloading professional resumes (CVs).

## 🎯 Features

- **User Authentication**: Sign up, login, logout with password reset
- **CV Builder**: Step-by-step form for personal info, education, experience, skills, languages
- **Template Selection**: Modern, classic, and creative templates
- **Live Preview**: Real-time CV preview with selected template
- **PDF Generation**: Download CV as PDF matching the preview exactly
- **User Dashboard**: Manage all CVs (create, edit, delete, download)
- **SaaS Features**: Freemium model with Stripe payments

## 🧱 Tech Stack

### Frontend
- React + Vite
- Tailwind CSS
- React Router
- Axios

### Backend
- Laravel
- Laravel Sanctum (API Authentication)
- <PERSON>vel DOMPDF (PDF Generation)
- MySQL/PostgreSQL

### Payment
- Stripe API

## 📁 Project Structure

```
cv-saas/
├── frontend/          # React + Vite application
├── backend/           # Laravel API
├── docs/             # Documentation
└── README.md
```

## 🚀 Getting Started

### Prerequisites
- Node.js (v18+)
- P<PERSON> (v8.1+)
- Composer
- MySQL/PostgreSQL

### Installation

1. Clone the repository
2. Set up the backend (Laravel)
3. Set up the frontend (React)
4. Configure environment variables
5. Run migrations
6. Start development servers

## 📋 Development Roadmap

### MVP Features ✅ COMPLETED
- [x] Project setup
- [x] User authentication (Login, Register, Password Reset)
- [x] Multi-step CV builder (Personal, Experience, Education, Skills, Languages)
- [x] Template system (Modern, Classic, Creative)
- [x] PDF generation with template matching
- [x] Payment integration (Stripe)
- [x] User dashboard with full CRUD
- [x] SaaS freemium model
- [x] Form validation and testing
- [x] Production deployment setup

## 🧪 Testing

- Form validation
- PDF layout accuracy
- Template consistency
- Authentication & protected routes
- Stripe test payments

## 📄 License

This project is proprietary software.
