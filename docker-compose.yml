version: '3.8'

services:
  # Frontend (React + Vite)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - VITE_API_URL=http://localhost:8000
    depends_on:
      - backend
    networks:
      - cv-builder-network

  # Backend (<PERSON>vel)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - APP_ENV=production
      - DB_HOST=database
      - DB_DATABASE=cv_builder
      - DB_USERNAME=cv_user
      - DB_PASSWORD=secure_password
    depends_on:
      - database
      - redis
    volumes:
      - ./backend/storage:/var/www/html/storage
      - ./backend/bootstrap/cache:/var/www/html/bootstrap/cache
    networks:
      - cv-builder-network

  # Database (MySQL)
  database:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=root_password
      - MYSQL_DATABASE=cv_builder
      - MYSQL_USER=cv_user
      - MYSQL_PASSWORD=secure_password
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/init:/docker-entrypoint-initdb.d
    ports:
      - "3306:3306"
    networks:
      - cv-builder-network

  # Redis (for caching and sessions)
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - cv-builder-network

  # Nginx (Reverse Proxy)
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - frontend
      - backend
    networks:
      - cv-builder-network

volumes:
  mysql_data:
  redis_data:

networks:
  cv-builder-network:
    driver: bridge
