function SimpleApp() {
  return (
    <div style={{ padding: '20px', backgroundColor: '#f5f5f5', minHeight: '100vh' }}>
      <h1 style={{ color: '#2563eb', fontSize: '2rem', marginBottom: '1rem' }}>Simple React App</h1>
      <p style={{ color: '#666', marginBottom: '2rem' }}>This is a simple JSX component test.</p>
      <div style={{ backgroundColor: 'white', padding: '1.5rem', borderRadius: '8px', boxShadow: '0 2px 4px rgba(0,0,0,0.1)' }}>
        <h2 style={{ fontSize: '1.25rem', fontWeight: 'bold', marginBottom: '1rem' }}>JSX Test</h2>
        <p>If you can see this, JSX is working correctly.</p>
        <button 
          style={{ 
            marginTop: '1rem', 
            backgroundColor: '#2563eb', 
            color: 'white', 
            padding: '0.5rem 1rem', 
            border: 'none', 
            borderRadius: '4px',
            cursor: 'pointer'
          }}
          onClick={() => alert('React event handling works!')}
        >
          Test Button
        </button>
      </div>
    </div>
  );
}

export default SimpleApp;
