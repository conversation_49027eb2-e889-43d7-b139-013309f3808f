<?php

namespace App\Services;

use App\Models\Cv;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\View;

class PdfService
{
    public function generateCvPdf(Cv $cv): string
    {
        // Load CV with template
        $cv->load('template');

        // Prepare data for the view
        $data = [
            'cv' => $cv,
            'template' => $cv->template,
            'personal_info' => $cv->personal_info,
            'education' => $cv->education ?? [],
            'experience' => $cv->experience ?? [],
            'skills' => $cv->skills ?? [],
            'languages' => $cv->languages ?? [],
            'additional_sections' => $cv->additional_sections ?? [],
        ];

        // Generate HTML from template
        $html = $this->generateHtmlFromTemplate($cv->template->name, $data);

        // For now, save as HTML file (in production, you'd use a proper PDF library)
        $filename = 'cv_' . $cv->id . '_' . time() . '.html';
        $path = 'cvs/' . $filename;

        // Save HTML to storage
        Storage::disk('public')->put($path, $html);

        return $path;
    }

    private function generateHtmlFromTemplate(string $templateName, array $data): string
    {
        // Check if template view exists, otherwise use default
        $viewName = "pdf.templates.{$templateName}";
        
        if (!View::exists($viewName)) {
            $viewName = 'pdf.templates.default';
        }

        return view($viewName, $data)->render();
    }

    public function downloadCvPdf(Cv $cv)
    {
        // Load CV with template
        $cv->load('template');

        // Prepare data for the view
        $data = [
            'cv' => $cv,
            'template' => $cv->template,
            'personal_info' => $cv->personal_info,
            'education' => $cv->education ?? [],
            'experience' => $cv->experience ?? [],
            'skills' => $cv->skills ?? [],
            'languages' => $cv->languages ?? [],
            'additional_sections' => $cv->additional_sections ?? [],
        ];

        // Generate HTML from template
        $html = $this->generateHtmlFromTemplate($cv->template->name, $data);

        $filename = ($cv->personal_info['fullName'] ?? 'CV') . '_CV.html';

        // Return HTML response that can be printed as PDF by browser
        return response($html, 200, [
            'Content-Type' => 'text/html',
            'Content-Disposition' => 'inline; filename="' . $filename . '"',
        ]);
    }
}
