<?php

namespace App\Services;

use App\Models\Cv;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\View;

class PdfService
{
    public function generateCvPdf(Cv $cv): string
    {
        // Load CV with template
        $cv->load('template');

        // Prepare data for the view
        $data = [
            'cv' => $cv,
            'template' => $cv->template,
            'personal_info' => $cv->personal_info,
            'education' => $cv->education ?? [],
            'experience' => $cv->experience ?? [],
            'skills' => $cv->skills ?? [],
            'languages' => $cv->languages ?? [],
            'additional_sections' => $cv->additional_sections ?? [],
        ];

        // Generate HTML from template
        $html = $this->generateHtmlFromTemplate($cv->template->name, $data);

        // Generate PDF
        $pdf = Pdf::loadHTML($html);
        $pdf->setPaper('A4', 'portrait');

        // Generate filename
        $filename = 'cv_' . $cv->id . '_' . time() . '.pdf';
        $path = 'pdfs/' . $filename;

        // Save PDF to storage
        Storage::disk('public')->put($path, $pdf->output());

        return $path;
    }

    private function generateHtmlFromTemplate(string $templateName, array $data): string
    {
        // Check if template view exists, otherwise use default
        $viewName = "pdf.templates.{$templateName}";
        
        if (!View::exists($viewName)) {
            $viewName = 'pdf.templates.default';
        }

        return view($viewName, $data)->render();
    }

    public function downloadCvPdf(Cv $cv): \Symfony\Component\HttpFoundation\BinaryFileResponse
    {
        $path = $this->generateCvPdf($cv);
        $fullPath = Storage::disk('public')->path($path);
        
        $filename = $cv->personal_info['name'] . '_CV.pdf';
        
        return response()->download($fullPath, $filename, [
            'Content-Type' => 'application/pdf',
        ]);
    }
}
