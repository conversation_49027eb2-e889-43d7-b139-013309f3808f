<?php

namespace App\Services;

use App\Models\User;
use App\Models\Subscription;
use Stripe\Stripe;
use Stripe\Checkout\Session;
use Stripe\Customer;
use Stripe\Price;
use Stripe\Product;
use Exception;

class StripeService
{
    public function __construct()
    {
        Stripe::setApiKey(config('services.stripe.secret'));
    }

    public function createCheckoutSession(User $user, string $priceId, string $successUrl, string $cancelUrl): array
    {
        try {
            // Create or get Stripe customer
            $customer = $this->getOrCreateCustomer($user);

            // Create checkout session
            $session = Session::create([
                'customer' => $customer->id,
                'payment_method_types' => ['card'],
                'line_items' => [
                    [
                        'price' => $priceId,
                        'quantity' => 1,
                    ],
                ],
                'mode' => 'subscription',
                'success_url' => $successUrl,
                'cancel_url' => $cancelUrl,
                'metadata' => [
                    'user_id' => $user->id,
                ],
            ]);

            return [
                'success' => true,
                'session_id' => $session->id,
                'checkout_url' => $session->url,
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    public function getOrCreateCustomer(User $user): Customer
    {
        // Check if user already has a Stripe customer ID
        if ($user->stripe_customer_id) {
            try {
                return Customer::retrieve($user->stripe_customer_id);
            } catch (Exception $e) {
                // Customer doesn't exist, create new one
            }
        }

        // Create new customer
        $customer = Customer::create([
            'email' => $user->email,
            'name' => $user->name,
            'metadata' => [
                'user_id' => $user->id,
            ],
        ]);

        // Save customer ID to user
        $user->update(['stripe_customer_id' => $customer->id]);

        return $customer;
    }

    public function handleWebhook(array $payload): array
    {
        try {
            $event = \Stripe\Webhook::constructEvent(
                json_encode($payload),
                request()->header('Stripe-Signature'),
                config('services.stripe.webhook_secret')
            );

            switch ($event->type) {
                case 'checkout.session.completed':
                    $this->handleCheckoutCompleted($event->data->object);
                    break;

                case 'customer.subscription.updated':
                    $this->handleSubscriptionUpdated($event->data->object);
                    break;

                case 'customer.subscription.deleted':
                    $this->handleSubscriptionCancelled($event->data->object);
                    break;

                case 'invoice.payment_succeeded':
                    $this->handlePaymentSucceeded($event->data->object);
                    break;

                case 'invoice.payment_failed':
                    $this->handlePaymentFailed($event->data->object);
                    break;
            }

            return ['success' => true];
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    private function handleCheckoutCompleted($session): void
    {
        $userId = $session->metadata->user_id;
        $user = User::find($userId);

        if (!$user) {
            return;
        }

        // Get subscription details from Stripe
        $stripeSubscription = \Stripe\Subscription::retrieve($session->subscription);
        $priceId = $stripeSubscription->items->data[0]->price->id;

        // Determine plan type based on price ID
        $planType = $this->getPlanTypeFromPriceId($priceId);
        $cvLimit = $this->getCvLimitFromPlanType($planType);

        // Update user's subscription
        $user->subscription()->updateOrCreate(
            ['user_id' => $user->id],
            [
                'stripe_subscription_id' => $stripeSubscription->id,
                'stripe_price_id' => $priceId,
                'plan_type' => $planType,
                'status' => 'active',
                'cv_limit' => $cvLimit,
                'current_period_start' => now()->createFromTimestamp($stripeSubscription->current_period_start),
                'current_period_end' => now()->createFromTimestamp($stripeSubscription->current_period_end),
            ]
        );
    }

    private function handleSubscriptionUpdated($subscription): void
    {
        $userSubscription = Subscription::where('stripe_subscription_id', $subscription->id)->first();

        if (!$userSubscription) {
            return;
        }

        $priceId = $subscription->items->data[0]->price->id;
        $planType = $this->getPlanTypeFromPriceId($priceId);
        $cvLimit = $this->getCvLimitFromPlanType($planType);

        $userSubscription->update([
            'stripe_price_id' => $priceId,
            'plan_type' => $planType,
            'status' => $subscription->status,
            'cv_limit' => $cvLimit,
            'current_period_start' => now()->createFromTimestamp($subscription->current_period_start),
            'current_period_end' => now()->createFromTimestamp($subscription->current_period_end),
        ]);
    }

    private function handleSubscriptionCancelled($subscription): void
    {
        $userSubscription = Subscription::where('stripe_subscription_id', $subscription->id)->first();

        if (!$userSubscription) {
            return;
        }

        $userSubscription->update([
            'status' => 'cancelled',
            'plan_type' => 'free',
            'cv_limit' => 1,
        ]);
    }

    private function handlePaymentSucceeded($invoice): void
    {
        // Handle successful payment (e.g., send receipt email)
    }

    private function handlePaymentFailed($invoice): void
    {
        // Handle failed payment (e.g., send notification)
    }

    private function getPlanTypeFromPriceId(string $priceId): string
    {
        // Map price IDs to plan types
        $priceMapping = [
            'price_premium_monthly' => 'premium',
            'price_enterprise_monthly' => 'enterprise',
        ];

        return $priceMapping[$priceId] ?? 'free';
    }

    private function getCvLimitFromPlanType(string $planType): int
    {
        return match ($planType) {
            'premium' => 999,
            'enterprise' => 9999,
            default => 1,
        };
    }

    public function getPlans(): array
    {
        return [
            [
                'id' => 'free',
                'name' => 'Free',
                'price' => 0,
                'currency' => 'usd',
                'interval' => null,
                'cv_limit' => 1,
                'features' => [
                    '1 CV creation',
                    'Basic templates',
                    'PDF download',
                    'Email support'
                ]
            ],
            [
                'id' => 'premium',
                'name' => 'Premium',
                'price' => 999, // $9.99 in cents
                'currency' => 'usd',
                'interval' => 'month',
                'stripe_price_id' => 'price_premium_monthly',
                'cv_limit' => 999,
                'features' => [
                    'Unlimited CV creation',
                    'All premium templates',
                    'Priority PDF generation',
                    'Priority support',
                    'Advanced customization'
                ]
            ],
            [
                'id' => 'enterprise',
                'name' => 'Enterprise',
                'price' => 2999, // $29.99 in cents
                'currency' => 'usd',
                'interval' => 'month',
                'stripe_price_id' => 'price_enterprise_monthly',
                'cv_limit' => 9999,
                'features' => [
                    'Everything in Premium',
                    'Team collaboration',
                    'Custom branding',
                    'API access',
                    'Dedicated support'
                ]
            ]
        ];
    }
}
