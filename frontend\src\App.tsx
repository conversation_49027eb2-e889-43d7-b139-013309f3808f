import React, { useState, useEffect, createContext, useContext } from 'react';

// Types
interface User {
  id: number;
  name: string;
  email: string;
  role: string;
  subscription?: {
    plan_type: string;
    cv_limit: number;
    is_active: boolean;
  };
}

interface AuthContextType {
  user: User | null;
  login: (email: string, password: string) => Promise<void>;
  register: (name: string, email: string, password: string, passwordConfirmation: string) => Promise<void>;
  logout: () => void;
  loading: boolean;
}

// Auth Context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Auth Provider
const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const token = localStorage.getItem('token');
    if (token) {
      // Verify token and get user
      fetchUser(token);
    } else {
      setLoading(false);
    }
  }, []);

  const fetchUser = async (token: string) => {
    try {
      const response = await fetch('http://localhost:8000/api/user', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setUser(data.user);
      } else {
        localStorage.removeItem('token');
      }
    } catch (error) {
      console.error('Error fetching user:', error);
      localStorage.removeItem('token');
    } finally {
      setLoading(false);
    }
  };

  const login = async (email: string, password: string) => {
    const response = await fetch('http://localhost:8000/api/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email, password }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Login failed');
    }

    const data = await response.json();
    localStorage.setItem('token', data.access_token);
    setUser(data.user);
  };

  const register = async (name: string, email: string, password: string, password_confirmation: string) => {
    const response = await fetch('http://localhost:8000/api/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ name, email, password, password_confirmation }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Registration failed');
    }

    const data = await response.json();
    localStorage.setItem('token', data.access_token);
    setUser(data.user);
  };

  const logout = () => {
    localStorage.removeItem('token');
    setUser(null);
  };

  return (
    <AuthContext.Provider value={{ user, login, register, logout, loading }}>
      {children}
    </AuthContext.Provider>
  );
};

// Simple Login Component
const Login = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const { login } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      await login(email, password);
      window.history.pushState({}, '', '/dashboard');
      window.location.reload();
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="max-w-md w-full bg-white rounded-lg shadow-md p-6">
        <h2 className="text-2xl font-bold text-center mb-6">Login to CV Builder</h2>
        {error && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {error}
          </div>
        )}
        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <label className="block text-gray-700 text-sm font-bold mb-2">
              Email
            </label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
              required
              disabled={loading}
            />
          </div>
          <div className="mb-6">
            <label className="block text-gray-700 text-sm font-bold mb-2">
              Password
            </label>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
              required
              disabled={loading}
            />
          </div>
          <button
            type="submit"
            disabled={loading}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
          >
            {loading ? 'Logging in...' : 'Login'}
          </button>
        </form>
        <p className="text-center mt-4">
          Don't have an account?{' '}
          <button
            onClick={() => {
              window.history.pushState({}, '', '/register');
              window.location.reload();
            }}
            className="text-blue-600 hover:underline"
          >
            Register
          </button>
        </p>
        <p className="text-center mt-2">
          <button
            onClick={() => {
              window.history.pushState({}, '', '/forgot-password');
              window.location.reload();
            }}
            className="text-blue-600 hover:underline text-sm"
          >
            Forgot Password?
          </button>
        </p>
      </div>
    </div>
  );
};

// Simple Register Component
const Register = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    password: '',
    confirmPassword: ''
  });
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const { register } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    setLoading(true);
    setError('');

    try {
      await register(formData.name, formData.email, formData.password, formData.confirmPassword);
      window.history.pushState({}, '', '/dashboard');
      window.location.reload();
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="max-w-md w-full bg-white rounded-lg shadow-md p-6">
        <h2 className="text-2xl font-bold text-center mb-6">Create Account</h2>
        {error && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {error}
          </div>
        )}
        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <label className="block text-gray-700 text-sm font-bold mb-2">
              Full Name
            </label>
            <input
              type="text"
              name="name"
              value={formData.name}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
              required
            />
          </div>
          <div className="mb-4">
            <label className="block text-gray-700 text-sm font-bold mb-2">
              Email
            </label>
            <input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
              required
            />
          </div>
          <div className="mb-4">
            <label className="block text-gray-700 text-sm font-bold mb-2">
              Password
            </label>
            <input
              type="password"
              name="password"
              value={formData.password}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
              required
            />
          </div>
          <div className="mb-6">
            <label className="block text-gray-700 text-sm font-bold mb-2">
              Confirm Password
            </label>
            <input
              type="password"
              name="confirmPassword"
              value={formData.confirmPassword}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
              required
            />
          </div>
          <button
            type="submit"
            disabled={loading}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
          >
            {loading ? 'Creating Account...' : 'Register'}
          </button>
        </form>
        <p className="text-center mt-4">
          Already have an account?{' '}
          <button
            onClick={() => {
              window.history.pushState({}, '', '/login');
              window.location.reload();
            }}
            className="text-blue-600 hover:underline"
          >
            Login
          </button>
        </p>
      </div>
    </div>
  );
};

// Enhanced Dashboard Component
const Dashboard = () => {
  const [cvs, setCvs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalCvs: 0,
    plan: 'Free',
    cvLimit: 1,
    downloads: 0
  });
  const { user, logout } = useAuth();

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      const token = localStorage.getItem('token');

      // Fetch user's CVs
      const cvsResponse = await fetch('http://localhost:8000/api/cvs', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (cvsResponse.ok) {
        const cvsData = await cvsResponse.json();
        setCvs(cvsData.cvs || []);

        // Update stats
        setStats({
          totalCvs: cvsData.cvs?.length || 0,
          plan: user?.subscription?.plan_type || 'Free',
          cvLimit: user?.subscription?.cv_limit || 1,
          downloads: cvsData.cvs?.length || 0 // Simplified for now
        });
      }
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const deleteCv = async (cvId: number) => {
    if (!confirm('Are you sure you want to delete this CV?')) return;

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`http://localhost:8000/api/cvs/${cvId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        setCvs(cvs.filter((cv: any) => cv.id !== cvId));
        setStats(prev => ({ ...prev, totalCvs: prev.totalCvs - 1 }));
      } else {
        alert('Error deleting CV');
      }
    } catch (error) {
      console.error('Error deleting CV:', error);
      alert('Error deleting CV');
    }
  };

  const downloadCv = async (cvId: number, cvTitle: string) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`http://localhost:8000/api/cvs/${cvId}/download`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const htmlContent = await response.text();

        // Open in new window for printing/saving as PDF
        const newWindow = window.open('', '_blank');
        if (newWindow) {
          newWindow.document.write(htmlContent);
          newWindow.document.close();

          // Add print styles and trigger print dialog
          setTimeout(() => {
            newWindow.print();
          }, 500);
        }
      } else {
        alert('Error downloading CV');
      }
    } catch (error) {
      console.error('Error downloading CV:', error);
      alert('Error downloading CV');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-bold text-blue-600">CV Builder SaaS</h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-gray-600">Welcome, {user?.name}</span>
              <button
                onClick={() => {
                  window.history.pushState({}, '', '/templates');
                  window.location.reload();
                }}
                className="text-gray-600 hover:text-gray-900"
              >
                Templates
              </button>
              <button
                onClick={() => {
                  if (stats.totalCvs >= stats.cvLimit) {
                    alert(`You've reached your CV limit (${stats.cvLimit}). Please upgrade to create more CVs.`);
                    return;
                  }
                  window.history.pushState({}, '', '/cv-builder');
                  window.location.reload();
                }}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
              >
                Create CV
              </button>
              <button
                onClick={logout}
                className="text-gray-600 hover:text-gray-900"
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold text-gray-900">Dashboard</h2>
            <div className="text-sm text-gray-500">
              {stats.totalCvs}/{stats.cvLimit} CVs used
            </div>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div className="bg-white p-6 rounded-lg shadow-md">
              <div className="flex items-center">
                <div className="p-3 rounded-full bg-blue-100 text-blue-600 mr-4">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-1">Total CVs</h3>
                  <p className="text-3xl font-bold text-blue-600">{stats.totalCvs}</p>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md">
              <div className="flex items-center">
                <div className={`p-3 rounded-full mr-4 ${
                  stats.plan === 'Free' ? 'bg-gray-100 text-gray-600' : 'bg-green-100 text-green-600'
                }`}>
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-1">Plan</h3>
                  <p className={`text-3xl font-bold ${
                    stats.plan === 'Free' ? 'text-gray-600' : 'text-green-600'
                  }`}>{stats.plan}</p>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-md">
              <div className="flex items-center">
                <div className="p-3 rounded-full bg-green-100 text-green-600 mr-4">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-1">Downloads</h3>
                  <p className="text-3xl font-bold text-green-600">{stats.downloads}</p>
                </div>
              </div>
            </div>
          </div>

          {/* CVs List */}
          <div className="bg-white rounded-lg shadow-md">
            <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
              <h3 className="text-lg font-semibold text-gray-900">Your CVs</h3>
              <button
                onClick={() => {
                  if (stats.totalCvs >= stats.cvLimit) {
                    alert(`You've reached your CV limit (${stats.cvLimit}). Please upgrade to create more CVs.`);
                    return;
                  }
                  window.history.pushState({}, '', '/cv-builder');
                  window.location.reload();
                }}
                className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 text-sm"
              >
                + New CV
              </button>
            </div>

            <div className="p-6">
              {loading ? (
                <div className="flex justify-center items-center h-32">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>
              ) : cvs.length === 0 ? (
                <div className="text-center py-12">
                  <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  <h3 className="mt-2 text-sm font-medium text-gray-900">No CVs yet</h3>
                  <p className="mt-1 text-sm text-gray-500">Get started by creating your first CV.</p>
                  <div className="mt-6">
                    <button
                      onClick={() => {
                        window.history.pushState({}, '', '/templates');
                        window.location.reload();
                      }}
                      className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
                    >
                      Create Your First CV
                    </button>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  {cvs.map((cv: any) => (
                    <div key={cv.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3">
                          <div className={`w-3 h-3 rounded-full ${cv.is_draft ? 'bg-yellow-400' : 'bg-green-400'}`}></div>
                          <div>
                            <h4 className="font-semibold text-gray-900">{cv.title}</h4>
                            <div className="flex items-center space-x-4 text-sm text-gray-500">
                              <span>Template: {cv.template?.name || 'Unknown'}</span>
                              <span>•</span>
                              <span>{cv.is_draft ? 'Draft' : 'Complete'}</span>
                              <span>•</span>
                              <span>Updated {new Date(cv.updated_at).toLocaleDateString()}</span>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => {
                            window.history.pushState({}, '', `/cv-builder?id=${cv.id}`);
                            window.location.reload();
                          }}
                          className="text-blue-600 hover:text-blue-800 px-3 py-1 rounded text-sm font-medium"
                        >
                          Edit
                        </button>
                        <button
                          onClick={() => downloadCv(cv.id, cv.title)}
                          className="text-green-600 hover:text-green-800 px-3 py-1 rounded text-sm font-medium"
                        >
                          Download
                        </button>
                        <button
                          onClick={() => deleteCv(cv.id)}
                          className="text-red-600 hover:text-red-800 px-3 py-1 rounded text-sm font-medium"
                        >
                          Delete
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Upgrade Prompt for Free Users */}
          {stats.plan === 'Free' && (
            <div className="mt-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-6 text-white">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold mb-2">Upgrade to Premium</h3>
                  <p className="text-blue-100">
                    Create unlimited CVs, access premium templates, and get priority support.
                  </p>
                </div>
                <button
                  onClick={() => {
                    window.history.pushState({}, '', '/pricing');
                    window.location.reload();
                  }}
                  className="bg-white text-blue-600 px-6 py-2 rounded-lg font-medium hover:bg-gray-100 transition-colors"
                >
                  Upgrade Now
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// Templates Component
const Templates = () => {
  const [templates, setTemplates] = useState([]);
  const [loading, setLoading] = useState(true);
  const { user } = useAuth();

  useEffect(() => {
    fetchTemplates();
  }, []);

  const fetchTemplates = async () => {
    try {
      const response = await fetch('http://localhost:8000/api/templates');
      if (response.ok) {
        const data = await response.json();
        setTemplates(data);
      }
    } catch (error) {
      console.error('Error fetching templates:', error);
    } finally {
      setLoading(false);
    }
  };

  const templatePreviews = [
    {
      id: 1,
      name: 'Modern',
      description: 'A clean, modern design perfect for tech professionals',
      color: 'bg-blue-50 border-blue-200',
      accent: 'text-blue-600',
      features: ['Clean typography', 'Minimalist design', 'ATS-friendly', 'Tech-focused layout']
    },
    {
      id: 2,
      name: 'Classic',
      description: 'A traditional, professional layout for corporate roles',
      color: 'bg-gray-50 border-gray-200',
      accent: 'text-gray-700',
      features: ['Traditional layout', 'Professional styling', 'Corporate-friendly', 'Timeless design']
    },
    {
      id: 3,
      name: 'Creative',
      description: 'A bold, creative design for designers and artists',
      color: 'bg-purple-50 border-purple-200',
      accent: 'text-purple-600',
      features: ['Creative layout', 'Visual elements', 'Portfolio-ready', 'Designer-focused']
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <button
                onClick={() => window.history.pushState({}, '', '/dashboard')}
                className="text-xl font-bold text-blue-600"
              >
                CV Builder SaaS
              </button>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={() => window.history.pushState({}, '', '/dashboard')}
                className="text-gray-600 hover:text-gray-900"
              >
                Dashboard
              </button>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Choose Your CV Template</h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Select from our professionally designed templates. Each template is optimized for different industries and career levels.
            </p>
          </div>

          {loading ? (
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {templatePreviews.map((template) => (
                <div key={template.id} className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                  {/* Template Preview */}
                  <div className={`h-80 ${template.color} border-b-2 ${template.color.replace('bg-', 'border-')} p-6 relative`}>
                    <div className="bg-white rounded-lg shadow-sm p-4 h-full overflow-hidden">
                      {/* Mock CV Preview */}
                      <div className="space-y-3">
                        <div className="text-center border-b pb-2">
                          <div className={`h-3 ${template.accent.replace('text-', 'bg-')} rounded mb-1 w-32 mx-auto`}></div>
                          <div className="h-2 bg-gray-300 rounded w-24 mx-auto mb-1"></div>
                          <div className="h-2 bg-gray-300 rounded w-28 mx-auto"></div>
                        </div>

                        <div className="space-y-2">
                          <div className={`h-2 ${template.accent.replace('text-', 'bg-')} rounded w-20`}></div>
                          <div className="h-1 bg-gray-300 rounded w-full"></div>
                          <div className="h-1 bg-gray-300 rounded w-4/5"></div>
                        </div>

                        <div className="space-y-2">
                          <div className={`h-2 ${template.accent.replace('text-', 'bg-')} rounded w-24`}></div>
                          <div className="space-y-1">
                            <div className="h-1 bg-gray-300 rounded w-3/4"></div>
                            <div className="h-1 bg-gray-200 rounded w-1/2"></div>
                            <div className="h-1 bg-gray-300 rounded w-5/6"></div>
                          </div>
                        </div>

                        <div className="grid grid-cols-2 gap-2">
                          <div className="space-y-1">
                            <div className={`h-2 ${template.accent.replace('text-', 'bg-')} rounded w-12`}></div>
                            <div className="h-1 bg-gray-300 rounded w-16"></div>
                            <div className="h-1 bg-gray-300 rounded w-14"></div>
                          </div>
                          <div className="space-y-1">
                            <div className={`h-2 ${template.accent.replace('text-', 'bg-')} rounded w-16`}></div>
                            <div className="h-1 bg-gray-300 rounded w-12"></div>
                            <div className="h-1 bg-gray-300 rounded w-18"></div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Template Badge */}
                    <div className={`absolute top-4 right-4 ${template.accent} bg-white px-2 py-1 rounded-full text-xs font-medium shadow-sm`}>
                      {template.name}
                    </div>
                  </div>

                  {/* Template Info */}
                  <div className="p-6">
                    <h3 className="text-xl font-bold text-gray-900 mb-2">{template.name} Template</h3>
                    <p className="text-gray-600 mb-4">{template.description}</p>

                    {/* Features */}
                    <div className="mb-6">
                      <h4 className="text-sm font-medium text-gray-700 mb-2">Features:</h4>
                      <ul className="text-sm text-gray-600 space-y-1">
                        {template.features.map((feature, index) => (
                          <li key={index} className="flex items-center">
                            <span className="w-1.5 h-1.5 bg-green-500 rounded-full mr-2"></span>
                            {feature}
                          </li>
                        ))}
                      </ul>
                    </div>

                    {/* Action Button */}
                    <button
                      onClick={() => {
                        if (!user) {
                          alert('Please login to use templates');
                          window.history.pushState({}, '', '/login');
                          window.location.reload();
                          return;
                        }
                        window.history.pushState({}, '', `/cv-builder?template=${template.id}`);
                        window.location.reload();
                      }}
                      className={`w-full ${template.accent.replace('text-', 'bg-')} text-white py-3 px-4 rounded-lg hover:opacity-90 transition-opacity font-medium`}
                    >
                      Use This Template
                    </button>

                    {/* Preview Button */}
                    <button
                      onClick={() => alert(`Preview for ${template.name} template - Feature coming soon!`)}
                      className="w-full mt-2 border border-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-50 transition-colors text-sm"
                    >
                      Preview Template
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* Additional Info */}
          <div className="mt-12 text-center">
            <div className="bg-blue-50 rounded-lg p-6 max-w-4xl mx-auto">
              <h3 className="text-lg font-semibold text-blue-900 mb-2">Need Help Choosing?</h3>
              <p className="text-blue-700 mb-4">
                All templates are ATS-friendly and professionally designed. Choose based on your industry and personal style.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <strong className="text-blue-900">Modern:</strong> Best for tech, startups, creative industries
                </div>
                <div>
                  <strong className="text-blue-900">Classic:</strong> Perfect for corporate, finance, legal sectors
                </div>
                <div>
                  <strong className="text-blue-900">Creative:</strong> Ideal for design, marketing, arts fields
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// CV Builder Component
const CVBuilder = () => {
  const [step, setStep] = useState(1);
  const [selectedTemplate, setSelectedTemplate] = useState(1);
  const [saving, setSaving] = useState(false);
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const [cvData, setCvData] = useState({
    title: 'My CV',
    personalInfo: {
      fullName: '',
      email: '',
      phone: '',
      address: '',
      city: '',
      country: '',
      linkedIn: '',
      website: '',
      summary: ''
    },
    experience: [
      {
        company: '',
        position: '',
        location: '',
        startDate: '',
        endDate: '',
        current: false,
        description: ''
      }
    ],
    education: [
      {
        school: '',
        degree: '',
        field: '',
        location: '',
        startDate: '',
        endDate: '',
        gpa: '',
        description: ''
      }
    ],
    skills: [
      {
        name: '',
        level: 'Beginner'
      }
    ],
    languages: [
      {
        name: '',
        level: 'Basic'
      }
    ]
  });

  const handlePersonalInfoChange = (field: string, value: string) => {
    setCvData({
      ...cvData,
      personalInfo: {
        ...cvData.personalInfo,
        [field]: value
      }
    });
  };

  const addExperience = () => {
    setCvData({
      ...cvData,
      experience: [
        ...cvData.experience,
        {
          company: '',
          position: '',
          location: '',
          startDate: '',
          endDate: '',
          current: false,
          description: ''
        }
      ]
    });
  };

  const updateExperience = (index: number, field: string, value: string | boolean) => {
    const newExperience = [...cvData.experience];
    newExperience[index] = { ...newExperience[index], [field]: value };
    setCvData({ ...cvData, experience: newExperience });
  };

  const removeExperience = (index: number) => {
    const newExperience = cvData.experience.filter((_, i) => i !== index);
    setCvData({ ...cvData, experience: newExperience });
  };

  const addEducation = () => {
    setCvData({
      ...cvData,
      education: [
        ...cvData.education,
        {
          school: '',
          degree: '',
          field: '',
          location: '',
          startDate: '',
          endDate: '',
          gpa: '',
          description: ''
        }
      ]
    });
  };

  const updateEducation = (index: number, field: string, value: string) => {
    const newEducation = [...cvData.education];
    newEducation[index] = { ...newEducation[index], [field]: value };
    setCvData({ ...cvData, education: newEducation });
  };

  const removeEducation = (index: number) => {
    const newEducation = cvData.education.filter((_, i) => i !== index);
    setCvData({ ...cvData, education: newEducation });
  };

  const addSkill = () => {
    setCvData({
      ...cvData,
      skills: [...cvData.skills, { name: '', level: 'Beginner' }]
    });
  };

  const updateSkill = (index: number, field: string, value: string) => {
    const newSkills = [...cvData.skills];
    newSkills[index] = { ...newSkills[index], [field]: value };
    setCvData({ ...cvData, skills: newSkills });
  };

  const removeSkill = (index: number) => {
    const newSkills = cvData.skills.filter((_, i) => i !== index);
    setCvData({ ...cvData, skills: newSkills });
  };

  const addLanguage = () => {
    setCvData({
      ...cvData,
      languages: [...cvData.languages, { name: '', level: 'Basic' }]
    });
  };

  const updateLanguage = (index: number, field: string, value: string) => {
    const newLanguages = [...cvData.languages];
    newLanguages[index] = { ...newLanguages[index], [field]: value };
    setCvData({ ...cvData, languages: newLanguages });
  };

  const removeLanguage = (index: number) => {
    const newLanguages = cvData.languages.filter((_, i) => i !== index);
    setCvData({ ...cvData, languages: newLanguages });
  };

  const saveDraft = async () => {
    setSaving(true);
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('http://localhost:8000/api/cvs', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: cvData.title,
          template_id: selectedTemplate,
          personal_info: cvData.personalInfo,
          experience: cvData.experience,
          education: cvData.education,
          skills: cvData.skills,
          languages: cvData.languages,
          is_draft: true
        }),
      });

      if (response.ok) {
        alert('Draft saved successfully!');
      }
    } catch (error) {
      console.error('Error saving draft:', error);
      alert('Error saving draft');
    } finally {
      setSaving(false);
    }
  };

  const generatePDF = async () => {
    try {
      const token = localStorage.getItem('token');

      // First, save the CV
      const saveResponse = await fetch('http://localhost:8000/api/cvs', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: cvData.title,
          template_id: selectedTemplate,
          personal_info: cvData.personalInfo,
          experience: cvData.experience,
          education: cvData.education,
          skills: cvData.skills,
          languages: cvData.languages,
          is_draft: false
        }),
      });

      if (saveResponse.ok) {
        const cvData = await saveResponse.json();

        // Then download the PDF
        const pdfResponse = await fetch(`http://localhost:8000/api/cvs/${cvData.cv.id}/download`, {
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });

        if (pdfResponse.ok) {
          const htmlContent = await pdfResponse.text();

          // Open in new window for printing/saving as PDF
          const newWindow = window.open('', '_blank');
          if (newWindow) {
            newWindow.document.write(htmlContent);
            newWindow.document.close();

            // Add print styles and trigger print dialog
            setTimeout(() => {
              newWindow.print();
            }, 500);
          }
        } else {
          alert('Error generating PDF');
        }
      } else {
        alert('Error saving CV');
      }
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Error generating PDF');
    }
  };

  const getSectionHeaderStyle = () => {
    return selectedTemplate === 1 ? 'text-blue-800 border-b-2 border-blue-200' :
           selectedTemplate === 2 ? 'text-gray-800 border-b border-gray-300' :
           'text-purple-800 border-b-2 border-purple-200';
  };

  const validateStep = (stepNumber: number): boolean => {
    const newErrors: {[key: string]: string} = {};

    if (stepNumber === 1) {
      // Personal Info validation
      if (!cvData.personalInfo.fullName.trim()) {
        newErrors.fullName = 'Full name is required';
      }
      if (!cvData.personalInfo.email.trim()) {
        newErrors.email = 'Email is required';
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(cvData.personalInfo.email)) {
        newErrors.email = 'Please enter a valid email address';
      }
      if (cvData.personalInfo.linkedIn && !/^https?:\/\/(www\.)?linkedin\.com\/in\//.test(cvData.personalInfo.linkedIn)) {
        newErrors.linkedIn = 'Please enter a valid LinkedIn URL';
      }
      if (cvData.personalInfo.website && !/^https?:\/\//.test(cvData.personalInfo.website)) {
        newErrors.website = 'Please enter a valid website URL (include http:// or https://)';
      }
    }

    if (stepNumber === 2) {
      // Experience validation
      cvData.experience.forEach((exp, index) => {
        if (exp.company || exp.position) {
          if (!exp.company.trim()) {
            newErrors[`experience_${index}_company`] = 'Company name is required';
          }
          if (!exp.position.trim()) {
            newErrors[`experience_${index}_position`] = 'Position is required';
          }
          if (!exp.startDate) {
            newErrors[`experience_${index}_startDate`] = 'Start date is required';
          }
          if (!exp.current && !exp.endDate) {
            newErrors[`experience_${index}_endDate`] = 'End date is required (or check "I currently work here")';
          }
        }
      });
    }

    if (stepNumber === 3) {
      // Education validation
      cvData.education.forEach((edu, index) => {
        if (edu.school || edu.degree) {
          if (!edu.school.trim()) {
            newErrors[`education_${index}_school`] = 'School/University is required';
          }
          if (!edu.degree.trim()) {
            newErrors[`education_${index}_degree`] = 'Degree is required';
          }
        }
      });
    }

    if (stepNumber === 4) {
      // Skills validation
      cvData.skills.forEach((skill, index) => {
        if (skill.name && !skill.name.trim()) {
          newErrors[`skill_${index}_name`] = 'Skill name cannot be empty';
        }
      });
    }

    if (stepNumber === 5) {
      // Languages validation
      cvData.languages.forEach((lang, index) => {
        if (lang.name && !lang.name.trim()) {
          newErrors[`language_${index}_name`] = 'Language name cannot be empty';
        }
      });
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNextStep = () => {
    if (validateStep(step)) {
      setStep(Math.min(5, step + 1));
    }
  };

  const handlePreviousStep = () => {
    setErrors({}); // Clear errors when going back
    setStep(Math.max(1, step - 1));
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <button
                onClick={() => window.history.pushState({}, '', '/dashboard')}
                className="text-xl font-bold text-blue-600"
              >
                CV Builder SaaS
              </button>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={() => window.history.pushState({}, '', '/templates')}
                className="text-gray-600 hover:text-gray-900"
              >
                Templates
              </button>
              <button
                onClick={generatePDF}
                className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700"
              >
                Download PDF
              </button>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-2xl font-bold text-gray-900">CV Builder</h2>
              <div className="flex space-x-2">
                <button
                  onClick={saveDraft}
                  disabled={saving}
                  className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 disabled:opacity-50"
                >
                  {saving ? 'Saving...' : 'Save Draft'}
                </button>
                <select
                  value={selectedTemplate}
                  onChange={(e) => setSelectedTemplate(Number(e.target.value))}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
                >
                  <option value={1}>Modern Template</option>
                  <option value={2}>Classic Template</option>
                  <option value={3}>Creative Template</option>
                </select>
              </div>
            </div>

            <div className="flex items-center">
              {[1, 2, 3, 4, 5].map((stepNumber) => (
                <div key={stepNumber} className="flex items-center">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center cursor-pointer ${
                    step >= stepNumber ? 'bg-blue-600 text-white' : 'bg-gray-300 text-gray-600'
                  }`} onClick={() => setStep(stepNumber)}>
                    {stepNumber}
                  </div>
                  {stepNumber < 5 && <div className="w-16 h-1 bg-gray-300 mx-2"></div>}
                </div>
              ))}
            </div>
            <div className="flex justify-between mt-2 text-sm text-gray-600">
              <span>Personal</span>
              <span>Experience</span>
              <span>Education</span>
              <span>Skills</span>
              <span>Languages</span>
            </div>
          </div>

            <div className="flex items-center">
              {[1, 2, 3, 4, 5].map((stepNumber) => (
                <div key={stepNumber} className="flex items-center">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center cursor-pointer ${
                    step >= stepNumber ? 'bg-blue-600 text-white' : 'bg-gray-300 text-gray-600'
                  }`} onClick={() => setStep(stepNumber)}>
                    {stepNumber}
                  </div>
                  {stepNumber < 5 && <div className="w-16 h-1 bg-gray-300 mx-2"></div>}
                </div>
              ))}
            </div>
            <div className="flex justify-between mt-2 text-sm text-gray-600">
              <span>Personal</span>
              <span>Experience</span>
              <span>Education</span>
              <span>Skills</span>
              <span>Languages</span>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div className="bg-white rounded-lg shadow-md p-6">
              <h3 className="text-lg font-semibold mb-4">
                Step {step}: {
                  step === 1 ? 'Personal Information' :
                  step === 2 ? 'Work Experience' :
                  step === 3 ? 'Education' :
                  step === 4 ? 'Skills' : 'Languages'
                }
              </h3>

              {step === 1 && (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Full Name *</label>
                      <input
                        type="text"
                        value={cvData.personalInfo.fullName}
                        onChange={(e) => handlePersonalInfoChange('fullName', e.target.value)}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none ${
                          errors.fullName ? 'border-red-500 focus:border-red-500' : 'border-gray-300 focus:border-blue-500'
                        }`}
                        required
                      />
                      {errors.fullName && <p className="text-red-500 text-sm mt-1">{errors.fullName}</p>}
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Email *</label>
                      <input
                        type="email"
                        value={cvData.personalInfo.email}
                        onChange={(e) => handlePersonalInfoChange('email', e.target.value)}
                        className={`w-full px-3 py-2 border rounded-lg focus:outline-none ${
                          errors.email ? 'border-red-500 focus:border-red-500' : 'border-gray-300 focus:border-blue-500'
                        }`}
                        required
                      />
                      {errors.email && <p className="text-red-500 text-sm mt-1">{errors.email}</p>}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Phone</label>
                      <input
                        type="tel"
                        value={cvData.personalInfo.phone}
                        onChange={(e) => handlePersonalInfoChange('phone', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Address</label>
                      <input
                        type="text"
                        value={cvData.personalInfo.address}
                        onChange={(e) => handlePersonalInfoChange('address', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">City</label>
                      <input
                        type="text"
                        value={cvData.personalInfo.city}
                        onChange={(e) => handlePersonalInfoChange('city', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Country</label>
                      <input
                        type="text"
                        value={cvData.personalInfo.country}
                        onChange={(e) => handlePersonalInfoChange('country', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">LinkedIn</label>
                      <input
                        type="url"
                        value={cvData.personalInfo.linkedIn}
                        onChange={(e) => handlePersonalInfoChange('linkedIn', e.target.value)}
                        placeholder="https://linkedin.com/in/yourprofile"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Website</label>
                      <input
                        type="url"
                        value={cvData.personalInfo.website}
                        onChange={(e) => handlePersonalInfoChange('website', e.target.value)}
                        placeholder="https://yourwebsite.com"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Professional Summary</label>
                    <textarea
                      value={cvData.personalInfo.summary}
                      onChange={(e) => handlePersonalInfoChange('summary', e.target.value)}
                      rows={4}
                      placeholder="Write a brief summary of your professional background and key achievements..."
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
                    />
                  </div>
                </div>
              )}

              {step === 2 && (
                <div className="space-y-6">
                  <div className="flex justify-between items-center">
                    <h4 className="text-md font-medium text-gray-900">Work Experience</h4>
                    <button
                      onClick={addExperience}
                      className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700"
                    >
                      + Add Experience
                    </button>
                  </div>

                  {cvData.experience.map((exp, index) => (
                    <div key={index} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex justify-between items-start mb-4">
                        <h5 className="font-medium text-gray-900">Experience {index + 1}</h5>
                        {cvData.experience.length > 1 && (
                          <button
                            onClick={() => removeExperience(index)}
                            className="text-red-600 hover:text-red-800 text-sm"
                          >
                            Remove
                          </button>
                        )}
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Company *</label>
                          <input
                            type="text"
                            value={exp.company}
                            onChange={(e) => updateExperience(index, 'company', e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
                            required
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Position *</label>
                          <input
                            type="text"
                            value={exp.position}
                            onChange={(e) => updateExperience(index, 'position', e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
                            required
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Location</label>
                          <input
                            type="text"
                            value={exp.location}
                            onChange={(e) => updateExperience(index, 'location', e.target.value)}
                            placeholder="City, Country"
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                          <input
                            type="month"
                            value={exp.startDate}
                            onChange={(e) => updateExperience(index, 'startDate', e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                          <input
                            type="month"
                            value={exp.endDate}
                            onChange={(e) => updateExperience(index, 'endDate', e.target.value)}
                            disabled={exp.current}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500 disabled:bg-gray-100"
                          />
                        </div>
                      </div>

                      <div className="mb-4">
                        <label className="flex items-center">
                          <input
                            type="checkbox"
                            checked={exp.current}
                            onChange={(e) => updateExperience(index, 'current', e.target.checked)}
                            className="mr-2"
                          />
                          <span className="text-sm text-gray-700">I currently work here</span>
                        </label>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                        <textarea
                          value={exp.description}
                          onChange={(e) => updateExperience(index, 'description', e.target.value)}
                          rows={4}
                          placeholder="Describe your responsibilities and achievements..."
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
                        />
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {step === 3 && (
                <div className="space-y-6">
                  <div className="flex justify-between items-center">
                    <h4 className="text-md font-medium text-gray-900">Education</h4>
                    <button
                      onClick={addEducation}
                      className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700"
                    >
                      + Add Education
                    </button>
                  </div>

                  {cvData.education.map((edu, index) => (
                    <div key={index} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex justify-between items-start mb-4">
                        <h5 className="font-medium text-gray-900">Education {index + 1}</h5>
                        {cvData.education.length > 1 && (
                          <button
                            onClick={() => removeEducation(index)}
                            className="text-red-600 hover:text-red-800 text-sm"
                          >
                            Remove
                          </button>
                        )}
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">School/University *</label>
                          <input
                            type="text"
                            value={edu.school}
                            onChange={(e) => updateEducation(index, 'school', e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
                            required
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Degree *</label>
                          <input
                            type="text"
                            value={edu.degree}
                            onChange={(e) => updateEducation(index, 'degree', e.target.value)}
                            placeholder="Bachelor's, Master's, PhD, etc."
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
                            required
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Field of Study</label>
                          <input
                            type="text"
                            value={edu.field}
                            onChange={(e) => updateEducation(index, 'field', e.target.value)}
                            placeholder="Computer Science, Business, etc."
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Location</label>
                          <input
                            type="text"
                            value={edu.location}
                            onChange={(e) => updateEducation(index, 'location', e.target.value)}
                            placeholder="City, Country"
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                          <input
                            type="month"
                            value={edu.startDate}
                            onChange={(e) => updateEducation(index, 'startDate', e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                          <input
                            type="month"
                            value={edu.endDate}
                            onChange={(e) => updateEducation(index, 'endDate', e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">GPA (Optional)</label>
                          <input
                            type="text"
                            value={edu.gpa}
                            onChange={(e) => updateEducation(index, 'gpa', e.target.value)}
                            placeholder="3.8/4.0"
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
                          />
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">Description (Optional)</label>
                        <textarea
                          value={edu.description}
                          onChange={(e) => updateEducation(index, 'description', e.target.value)}
                          rows={3}
                          placeholder="Relevant coursework, achievements, honors..."
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
                        />
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {step === 4 && (
                <div className="space-y-6">
                  <div className="flex justify-between items-center">
                    <h4 className="text-md font-medium text-gray-900">Skills</h4>
                    <button
                      onClick={addSkill}
                      className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700"
                    >
                      + Add Skill
                    </button>
                  </div>

                  {cvData.skills.map((skill, index) => (
                    <div key={index} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex justify-between items-start mb-4">
                        <h5 className="font-medium text-gray-900">Skill {index + 1}</h5>
                        {cvData.skills.length > 1 && (
                          <button
                            onClick={() => removeSkill(index)}
                            className="text-red-600 hover:text-red-800 text-sm"
                          >
                            Remove
                          </button>
                        )}
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Skill Name *</label>
                          <input
                            type="text"
                            value={skill.name}
                            onChange={(e) => updateSkill(index, 'name', e.target.value)}
                            placeholder="JavaScript, Project Management, etc."
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
                            required
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Proficiency Level</label>
                          <select
                            value={skill.level}
                            onChange={(e) => updateSkill(index, 'level', e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
                          >
                            <option value="Beginner">Beginner</option>
                            <option value="Intermediate">Intermediate</option>
                            <option value="Advanced">Advanced</option>
                            <option value="Expert">Expert</option>
                          </select>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {step === 5 && (
                <div className="space-y-6">
                  <div className="flex justify-between items-center">
                    <h4 className="text-md font-medium text-gray-900">Languages</h4>
                    <button
                      onClick={addLanguage}
                      className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700"
                    >
                      + Add Language
                    </button>
                  </div>

                  {cvData.languages.map((language, index) => (
                    <div key={index} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex justify-between items-start mb-4">
                        <h5 className="font-medium text-gray-900">Language {index + 1}</h5>
                        {cvData.languages.length > 1 && (
                          <button
                            onClick={() => removeLanguage(index)}
                            className="text-red-600 hover:text-red-800 text-sm"
                          >
                            Remove
                          </button>
                        )}
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Language *</label>
                          <input
                            type="text"
                            value={language.name}
                            onChange={(e) => updateLanguage(index, 'name', e.target.value)}
                            placeholder="English, Spanish, French, etc."
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
                            required
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">Proficiency Level</label>
                          <select
                            value={language.level}
                            onChange={(e) => updateLanguage(index, 'level', e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
                          >
                            <option value="Basic">Basic</option>
                            <option value="Conversational">Conversational</option>
                            <option value="Fluent">Fluent</option>
                            <option value="Native">Native</option>
                          </select>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              <div className="flex justify-between mt-6">
                <button
                  onClick={handlePreviousStep}
                  disabled={step === 1}
                  className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50"
                >
                  Previous
                </button>
                <button
                  onClick={handleNextStep}
                  disabled={step === 5}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
                >
                  {step === 5 ? 'Complete' : 'Next'}
                </button>
              </div>

              {/* Display validation errors */}
              {Object.keys(errors).length > 0 && (
                <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                  <h4 className="text-red-800 font-medium mb-2">Please fix the following errors:</h4>
                  <ul className="text-red-700 text-sm space-y-1">
                    {Object.values(errors).map((error, index) => (
                      <li key={index}>• {error}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>

            <div className="bg-white rounded-lg shadow-md p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold">CV Preview</h3>
                <div className="text-sm text-gray-500">
                  Template: {selectedTemplate === 1 ? 'Modern' : selectedTemplate === 2 ? 'Classic' : 'Creative'}
                </div>
              </div>

              <div className={`border-2 border-dashed rounded-lg p-6 min-h-96 max-h-96 overflow-y-auto ${
                selectedTemplate === 1 ? 'border-blue-300 bg-blue-50' :
                selectedTemplate === 2 ? 'border-gray-300 bg-gray-50' :
                'border-purple-300 bg-purple-50'
              }`}>
                <div className="space-y-6 bg-white rounded-lg p-4 shadow-sm">
                  {/* Header */}
                  <div className={`text-center pb-4 ${
                    selectedTemplate === 1 ? 'border-b-2 border-blue-200' :
                    selectedTemplate === 2 ? 'border-b border-gray-300' :
                    'border-b-2 border-purple-200'
                  }`}>
                    <h4 className={`text-2xl font-bold mb-2 ${
                      selectedTemplate === 1 ? 'text-blue-900' :
                      selectedTemplate === 2 ? 'text-gray-800' :
                      'text-purple-900'
                    }`}>
                      {cvData.personalInfo.fullName || 'Your Name'}
                    </h4>
                    <div className="text-gray-600 text-sm space-y-1">
                      {cvData.personalInfo.email && <p>{cvData.personalInfo.email}</p>}
                      {cvData.personalInfo.phone && <p>{cvData.personalInfo.phone}</p>}
                      {(cvData.personalInfo.city || cvData.personalInfo.country) && (
                        <p>{[cvData.personalInfo.city, cvData.personalInfo.country].filter(Boolean).join(', ')}</p>
                      )}
                      {cvData.personalInfo.linkedIn && <p>{cvData.personalInfo.linkedIn}</p>}
                      {cvData.personalInfo.website && <p>{cvData.personalInfo.website}</p>}
                    </div>
                  </div>

                  {/* Professional Summary */}
                  {cvData.personalInfo.summary && (
                    <div className="text-left">
                      <h5 className={`font-semibold mb-2 pb-1 ${getSectionHeaderStyle()}`}>Professional Summary</h5>
                      <p className="text-gray-600 text-sm">{cvData.personalInfo.summary}</p>
                    </div>
                  )}

                  {/* Experience */}
                  {cvData.experience.some(exp => exp.company || exp.position) && (
                    <div className="text-left">
                      <h5 className={`font-semibold mb-2 pb-1 ${getSectionHeaderStyle()}`}>Work Experience</h5>
                      <div className="space-y-3">
                        {cvData.experience.filter(exp => exp.company || exp.position).map((exp, index) => (
                          <div key={index} className="text-sm">
                            <div className="font-medium text-gray-800">{exp.position}</div>
                            <div className="text-gray-600">{exp.company} {exp.location && `• ${exp.location}`}</div>
                            <div className="text-gray-500 text-xs">
                              {exp.startDate} - {exp.current ? 'Present' : exp.endDate || 'Present'}
                            </div>
                            {exp.description && <p className="text-gray-600 mt-1">{exp.description}</p>}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Education */}
                  {cvData.education.some(edu => edu.school || edu.degree) && (
                    <div className="text-left">
                      <h5 className={`font-semibold mb-2 pb-1 ${getSectionHeaderStyle()}`}>Education</h5>
                      <div className="space-y-3">
                        {cvData.education.filter(edu => edu.school || edu.degree).map((edu, index) => (
                          <div key={index} className="text-sm">
                            <div className="font-medium text-gray-800">{edu.degree} {edu.field && `in ${edu.field}`}</div>
                            <div className="text-gray-600">{edu.school} {edu.location && `• ${edu.location}`}</div>
                            <div className="text-gray-500 text-xs">
                              {edu.startDate} - {edu.endDate} {edu.gpa && `• GPA: ${edu.gpa}`}
                            </div>
                            {edu.description && <p className="text-gray-600 mt-1">{edu.description}</p>}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Skills */}
                  {cvData.skills.some(skill => skill.name) && (
                    <div className="text-left">
                      <h5 className={`font-semibold mb-2 pb-1 ${getSectionHeaderStyle()}`}>Skills</h5>
                      <div className="grid grid-cols-2 gap-2">
                        {cvData.skills.filter(skill => skill.name).map((skill, index) => (
                          <div key={index} className="text-sm">
                            <span className="font-medium text-gray-700">{skill.name}</span>
                            <span className="text-gray-500 ml-2">({skill.level})</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Languages */}
                  {cvData.languages.some(lang => lang.name) && (
                    <div className="text-left">
                      <h5 className={`font-semibold mb-2 pb-1 ${getSectionHeaderStyle()}`}>Languages</h5>
                      <div className="grid grid-cols-2 gap-2">
                        {cvData.languages.filter(lang => lang.name).map((lang, index) => (
                          <div key={index} className="text-sm">
                            <span className="font-medium text-gray-700">{lang.name}</span>
                            <span className="text-gray-500 ml-2">({lang.level})</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const AppContent = () => {
  const [currentPage, setCurrentPage] = useState('login');
  const { user, loading } = useAuth();

  // Simple routing based on URL
  React.useEffect(() => {
    const path = window.location.pathname;
    if (path === '/register') setCurrentPage('register');
    else if (path === '/dashboard') setCurrentPage('dashboard');
    else if (path === '/templates') setCurrentPage('templates');
    else if (path === '/cv-builder') setCurrentPage('cv-builder');
    else if (path === '/pricing') setCurrentPage('pricing');
    else if (path === '/forgot-password') setCurrentPage('forgot-password');
    else setCurrentPage('login');
  }, []);

  // Handle navigation
  React.useEffect(() => {
    const handlePopState = () => {
      const path = window.location.pathname;
      if (path === '/register') setCurrentPage('register');
      else if (path === '/dashboard') setCurrentPage('dashboard');
      else if (path === '/templates') setCurrentPage('templates');
      else if (path === '/cv-builder') setCurrentPage('cv-builder');
      else if (path === '/pricing') setCurrentPage('pricing');
      else if (path === '/forgot-password') setCurrentPage('forgot-password');
      else setCurrentPage('login');
    };

    window.addEventListener('popstate', handlePopState);
    return () => window.removeEventListener('popstate', handlePopState);
  }, []);

  // Redirect authenticated users away from auth pages
  React.useEffect(() => {
    if (user && (currentPage === 'login' || currentPage === 'register')) {
      window.history.pushState({}, '', '/dashboard');
      setCurrentPage('dashboard');
    }
  }, [user, currentPage]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // Protected routes
  if (user && (currentPage === 'dashboard' || currentPage === 'templates' || currentPage === 'cv-builder' || currentPage === 'pricing')) {
    if (currentPage === 'dashboard') return <Dashboard />;
    if (currentPage === 'templates') return <Templates />;
    if (currentPage === 'cv-builder') return <CVBuilder />;
    if (currentPage === 'pricing') return <Pricing />;
  }

  // Public routes
  if (currentPage === 'register') return <Register />;
  if (currentPage === 'forgot-password') return <ForgotPassword />;
  return <Login />;
};

// Pricing Component
const Pricing = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);

  const plans = [
    {
      name: 'Free',
      price: '$0',
      period: 'forever',
      features: [
        '1 CV creation',
        'Basic templates',
        'PDF download',
        'Email support'
      ],
      limitations: [
        'Limited to 1 CV',
        'Basic templates only',
        'Standard support'
      ],
      current: user?.subscription?.plan_type === 'free',
      buttonText: 'Current Plan',
      buttonDisabled: true
    },
    {
      name: 'Premium',
      price: '$9.99',
      period: 'month',
      features: [
        'Unlimited CV creation',
        'All premium templates',
        'Priority PDF generation',
        'Priority support',
        'Advanced customization',
        'Export to multiple formats'
      ],
      limitations: [],
      current: user?.subscription?.plan_type === 'premium',
      buttonText: user?.subscription?.plan_type === 'premium' ? 'Current Plan' : 'Upgrade Now',
      buttonDisabled: user?.subscription?.plan_type === 'premium'
    },
    {
      name: 'Enterprise',
      price: '$29.99',
      period: 'month',
      features: [
        'Everything in Premium',
        'Team collaboration',
        'Custom branding',
        'API access',
        'Dedicated support',
        'Custom templates'
      ],
      limitations: [],
      current: user?.subscription?.plan_type === 'enterprise',
      buttonText: user?.subscription?.plan_type === 'enterprise' ? 'Current Plan' : 'Contact Sales',
      buttonDisabled: false
    }
  ];

  const handleUpgrade = async (planName: string) => {
    if (planName === 'Enterprise') {
      alert('Please contact sales for Enterprise plans: <EMAIL>');
      return;
    }

    if (planName === 'Free') {
      return; // Already on free plan
    }

    setLoading(true);
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('http://localhost:8000/api/subscription/checkout', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          plan_id: planName.toLowerCase()
        }),
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success && data.checkout_url) {
          // Redirect to Stripe Checkout
          window.location.href = data.checkout_url;
        } else {
          alert('Error creating checkout session');
        }
      } else {
        const error = await response.json();
        alert(error.message || 'Error processing upgrade');
      }
    } catch (error) {
      console.error('Error upgrading:', error);
      alert('Error processing upgrade');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <button
                onClick={() => {
                  window.history.pushState({}, '', '/dashboard');
                  window.location.reload();
                }}
                className="text-xl font-bold text-blue-600"
              >
                CV Builder SaaS
              </button>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={() => {
                  window.history.pushState({}, '', '/dashboard');
                  window.location.reload();
                }}
                className="text-gray-600 hover:text-gray-900"
              >
                Dashboard
              </button>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Choose Your Plan</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Select the perfect plan for your CV creation needs. Upgrade or downgrade at any time.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {plans.map((plan, index) => (
            <div key={plan.name} className={`bg-white rounded-xl shadow-lg overflow-hidden ${
              plan.name === 'Premium' ? 'ring-2 ring-blue-500 transform scale-105' : ''
            }`}>
              {plan.name === 'Premium' && (
                <div className="bg-blue-500 text-white text-center py-2 text-sm font-medium">
                  Most Popular
                </div>
              )}

              <div className="p-8">
                <div className="text-center mb-8">
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">{plan.name}</h3>
                  <div className="mb-4">
                    <span className="text-4xl font-bold text-gray-900">{plan.price}</span>
                    <span className="text-gray-600">/{plan.period}</span>
                  </div>
                  {plan.current && (
                    <span className="inline-block bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                      Current Plan
                    </span>
                  )}
                </div>

                <div className="mb-8">
                  <h4 className="font-semibold text-gray-900 mb-4">Features included:</h4>
                  <ul className="space-y-3">
                    {plan.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center">
                        <svg className="w-5 h-5 text-green-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        <span className="text-gray-700">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                <button
                  onClick={() => handleUpgrade(plan.name)}
                  disabled={plan.buttonDisabled || loading}
                  className={`w-full py-3 px-4 rounded-lg font-medium transition-colors ${
                    plan.current
                      ? 'bg-gray-100 text-gray-500 cursor-not-allowed'
                      : plan.name === 'Premium'
                      ? 'bg-blue-600 text-white hover:bg-blue-700'
                      : 'bg-gray-900 text-white hover:bg-gray-800'
                  } disabled:opacity-50`}
                >
                  {loading ? 'Processing...' : plan.buttonText}
                </button>
              </div>
            </div>
          ))}
        </div>

        {/* FAQ Section */}
        <div className="mt-16">
          <h3 className="text-2xl font-bold text-gray-900 text-center mb-8">Frequently Asked Questions</h3>
          <div className="max-w-3xl mx-auto space-y-6">
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <h4 className="font-semibold text-gray-900 mb-2">Can I change my plan anytime?</h4>
              <p className="text-gray-600">Yes, you can upgrade or downgrade your plan at any time. Changes take effect immediately.</p>
            </div>
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <h4 className="font-semibold text-gray-900 mb-2">What happens to my CVs if I downgrade?</h4>
              <p className="text-gray-600">Your existing CVs remain accessible, but you won't be able to create new ones beyond the plan limit.</p>
            </div>
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <h4 className="font-semibold text-gray-900 mb-2">Is there a free trial?</h4>
              <p className="text-gray-600">Yes! You can start with our free plan and upgrade when you need more features.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Forgot Password Component
const ForgotPassword = () => {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    setMessage('');

    try {
      const response = await fetch('http://localhost:8000/api/forgot-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      if (response.ok) {
        const data = await response.json();
        setMessage(data.message);
      } else {
        const error = await response.json();
        setError(error.message || 'Failed to send reset email');
      }
    } catch (err: any) {
      setError('Network error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="max-w-md w-full bg-white rounded-lg shadow-md p-6">
        <h2 className="text-2xl font-bold text-center mb-6">Reset Password</h2>

        {error && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {error}
          </div>
        )}

        {message && (
          <div className="mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded">
            {message}
          </div>
        )}

        <form onSubmit={handleSubmit}>
          <div className="mb-6">
            <label className="block text-gray-700 text-sm font-bold mb-2">
              Email Address
            </label>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
              required
              disabled={loading}
            />
          </div>

          <button
            type="submit"
            disabled={loading}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
          >
            {loading ? 'Sending...' : 'Send Reset Link'}
          </button>
        </form>

        <p className="text-center mt-4">
          Remember your password?{' '}
          <button
            onClick={() => {
              window.history.pushState({}, '', '/login');
              window.location.reload();
            }}
            className="text-blue-600 hover:underline"
          >
            Login
          </button>
        </p>
      </div>
    </div>
  );
};

function App() {
  return (
    <AuthProvider>
      <AppContent />
    </AuthProvider>
  );
}

export default App;
