import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Layout } from '../components/Layout';
import { useAuth } from '../contexts/AuthContext';
import { cvService } from '../services/cvService';
import { CV } from '../types';

export const Dashboard: React.FC = () => {
  const { user } = useAuth();
  const [cvs, setCvs] = useState<CV[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchCvs = async () => {
      try {
        const response = await cvService.getCVs();
        setCvs(response.cvs);
      } catch (error) {
        console.error('Error fetching CVs:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchCvs();
  }, []);

  const handleDownload = async (cv: CV) => {
    try {
      const blob = await cvService.downloadCV(cv.id);
      cvService.downloadCVFile(blob, `${cv.title}.pdf`);
    } catch (error) {
      console.error('Error downloading CV:', error);
    }
  };

  const handleDelete = async (cvId: number) => {
    if (window.confirm('Are you sure you want to delete this CV?')) {
      try {
        await cvService.deleteCV(cvId);
        setCvs(cvs.filter(cv => cv.id !== cvId));
      } catch (error) {
        console.error('Error deleting CV:', error);
      }
    }
  };

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
            <p className="text-gray-600">Welcome back, {user?.name}!</p>
          </div>
          <Link
            to="/cv-builder"
            className="btn-primary"
          >
            Create New CV
          </Link>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="card">
            <h3 className="text-lg font-medium text-gray-900">Total CVs</h3>
            <p className="text-3xl font-bold text-primary-600">{cvs.length}</p>
          </div>
          <div className="card">
            <h3 className="text-lg font-medium text-gray-900">Plan</h3>
            <p className="text-3xl font-bold text-primary-600">
              {user?.subscription?.plan_type === 'premium' ? 'Premium' : 'Free'}
            </p>
          </div>
          <div className="card">
            <h3 className="text-lg font-medium text-gray-900">CV Limit</h3>
            <p className="text-3xl font-bold text-primary-600">
              {cvs.length}/{user?.subscription?.cv_limit === -1 ? '∞' : user?.subscription?.cv_limit}
            </p>
          </div>
        </div>

        {/* CVs List */}
        <div className="card">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold text-gray-900">Your CVs</h2>
            <Link to="/templates" className="btn-secondary">
              Browse Templates
            </Link>
          </div>

          {loading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
            </div>
          ) : cvs.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-500 mb-4">You haven't created any CVs yet.</p>
              <Link to="/cv-builder" className="btn-primary">
                Create Your First CV
              </Link>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {cvs.map((cv) => (
                <div key={cv.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                  <div className="flex justify-between items-start mb-3">
                    <h3 className="font-medium text-gray-900 truncate">{cv.title}</h3>
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      cv.is_draft
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-green-100 text-green-800'
                    }`}>
                      {cv.is_draft ? 'Draft' : 'Complete'}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 mb-3">
                    Template: {cv.template?.display_name || 'Unknown'}
                  </p>
                  <p className="text-xs text-gray-500 mb-4">
                    Last edited: {cv.last_edited_at ? new Date(cv.last_edited_at).toLocaleDateString() : 'Never'}
                  </p>
                  <div className="flex space-x-2">
                    <Link
                      to={`/cv-builder/${cv.id}`}
                      className="flex-1 text-center btn-secondary text-sm py-2"
                    >
                      Edit
                    </Link>
                    <button
                      onClick={() => handleDownload(cv)}
                      className="flex-1 btn-primary text-sm py-2"
                    >
                      Download
                    </button>
                    <button
                      onClick={() => handleDelete(cv.id)}
                      className="px-3 py-2 text-red-600 hover:bg-red-50 rounded text-sm"
                    >
                      🗑️
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </Layout>
  );
};
