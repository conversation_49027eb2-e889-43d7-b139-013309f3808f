import { apiClient } from './api';
import { ENDPOINTS } from '../config/api';
import { CV } from '../types';

export interface CreateCVData {
  title: string;
  template_id: number;
  personal_info: any;
  education?: any[];
  experience?: any[];
  skills?: any[];
  languages?: any[];
  additional_sections?: any;
  is_draft?: boolean;
}

export interface UpdateCVData extends Partial<CreateCVData> {}

class CVService {
  async getCVs(): Promise<{ cvs: CV[] }> {
    return await apiClient.get<{ cvs: CV[] }>(ENDPOINTS.CVS);
  }

  async getCV(id: number): Promise<{ cv: CV }> {
    return await apiClient.get<{ cv: CV }>(`${ENDPOINTS.CVS}/${id}`);
  }

  async createCV(data: CreateCVData): Promise<{ message: string; cv: CV }> {
    return await apiClient.post<{ message: string; cv: CV }>(ENDPOINTS.CVS, data);
  }

  async updateCV(id: number, data: UpdateCVData): Promise<{ message: string; cv: CV }> {
    return await apiClient.put<{ message: string; cv: CV }>(`${ENDPOINTS.CVS}/${id}`, data);
  }

  async deleteCV(id: number): Promise<{ message: string }> {
    return await apiClient.delete<{ message: string }>(`${ENDPOINTS.CVS}/${id}`);
  }

  async downloadCV(id: number): Promise<Blob> {
    return await apiClient.download(ENDPOINTS.CV_DOWNLOAD(id));
  }

  downloadCVFile(blob: Blob, filename: string) {
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  }
}

export const cvService = new CVService();
