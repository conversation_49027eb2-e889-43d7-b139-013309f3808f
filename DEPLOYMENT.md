# 🚀 CV Builder SaaS - Production Deployment Guide

This guide will help you deploy the CV Builder SaaS application to production.

## 📋 Prerequisites

- **Server**: Ubuntu 20.04+ or CentOS 8+ with at least 2GB RAM
- **Docker**: Version 20.10+
- **Docker Compose**: Version 2.0+
- **Domain**: A registered domain name pointing to your server
- **SSL Certificate**: Let's Encrypt or commercial SSL certificate
- **Stripe Account**: For payment processing

## 🛠️ Quick Deployment

### 1. Clone the Repository

```bash
git clone https://github.com/your-username/cv-builder-saas.git
cd cv-builder-saas
```

### 2. Configure Environment

```bash
# Copy and edit the production environment file
cp backend/.env.production backend/.env

# Edit the configuration
nano backend/.env
```

**Important Environment Variables to Update:**

```env
APP_URL=https://your-domain.com
DB_HOST=database
DB_DATABASE=cv_builder
DB_USERNAME=cv_user
DB_PASSWORD=your-secure-password

# Stripe Production Keys
STRIPE_KEY=pk_live_your_publishable_key
STRIPE_SECRET=sk_live_your_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# Email Configuration
MAIL_HOST=your-smtp-host
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-email-password

# Security
SESSION_DOMAIN=.your-domain.com
SANCTUM_STATEFUL_DOMAINS=your-domain.com,www.your-domain.com
```

### 3. Deploy the Application

```bash
# Make deployment script executable
chmod +x deploy.sh

# Run deployment
./deploy.sh your-domain.com production
```

### 4. Configure SSL (Let's Encrypt)

```bash
# Install Certbot
sudo apt update
sudo apt install certbot python3-certbot-nginx

# Generate SSL certificate
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

## 🔧 Manual Deployment Steps

### 1. Server Setup

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 2. Application Setup

```bash
# Clone repository
git clone https://github.com/your-username/cv-builder-saas.git
cd cv-builder-saas

# Set up environment
cp backend/.env.production backend/.env
# Edit backend/.env with your configuration

# Generate Laravel key
cd backend
php artisan key:generate
cd ..
```

### 3. Database Setup

```bash
# Start database container
docker-compose up -d database

# Wait for database to be ready
sleep 30

# Run migrations
docker-compose exec backend php artisan migrate --force
docker-compose exec backend php artisan db:seed --force
```

### 4. Start All Services

```bash
# Build and start all containers
docker-compose up -d

# Check status
docker-compose ps
```

## 🔐 Security Configuration

### 1. Firewall Setup

```bash
# Configure UFW
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable
```

### 2. SSL Configuration

Create `/etc/nginx/sites-available/cv-builder`:

```nginx
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;

    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;

    # Security headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;

    # Frontend
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Backend API
    location /api {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 3. Database Security

```bash
# Secure MySQL installation
docker-compose exec database mysql_secure_installation

# Create database backup user
docker-compose exec database mysql -u root -p
CREATE USER 'backup'@'%' IDENTIFIED BY 'backup_password';
GRANT SELECT, LOCK TABLES ON cv_builder.* TO 'backup'@'%';
FLUSH PRIVILEGES;
```

## 💳 Stripe Configuration

### 1. Webhook Setup

1. Go to Stripe Dashboard → Webhooks
2. Add endpoint: `https://your-domain.com/api/subscription/webhook`
3. Select events:
   - `checkout.session.completed`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
   - `invoice.payment_succeeded`
   - `invoice.payment_failed`

### 2. Product Setup

Create products in Stripe Dashboard:
- **Premium Plan**: $9.99/month
- **Enterprise Plan**: $29.99/month

Update price IDs in `backend/app/Services/StripeService.php`

## 📊 Monitoring & Maintenance

### 1. Health Checks

```bash
# Check application health
curl https://your-domain.com/api/health

# Check container status
docker-compose ps

# View logs
docker-compose logs -f backend
docker-compose logs -f frontend
```

### 2. Backup Strategy

```bash
# Database backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
docker-compose exec database mysqldump -u backup -pbackup_password cv_builder > backup_$DATE.sql
aws s3 cp backup_$DATE.sql s3://your-backup-bucket/
rm backup_$DATE.sql
```

### 3. Updates

```bash
# Update application
git pull origin main
docker-compose build --no-cache
docker-compose up -d
docker-compose exec backend php artisan migrate --force
docker-compose exec backend php artisan config:cache
```

## 🚨 Troubleshooting

### Common Issues

1. **Database Connection Failed**
   ```bash
   docker-compose logs database
   docker-compose restart database
   ```

2. **SSL Certificate Issues**
   ```bash
   sudo certbot renew --dry-run
   sudo nginx -t
   sudo systemctl reload nginx
   ```

3. **Storage Permissions**
   ```bash
   docker-compose exec backend chown -R www-data:www-data storage
   docker-compose exec backend chmod -R 755 storage
   ```

## 📞 Support

For deployment support:
- Email: <EMAIL>
- Documentation: https://docs.your-domain.com
- GitHub Issues: https://github.com/your-username/cv-builder-saas/issues

## 🎉 Post-Deployment Checklist

- [ ] Application accessible via HTTPS
- [ ] SSL certificate valid and auto-renewing
- [ ] Database migrations completed
- [ ] Stripe webhooks configured
- [ ] Email sending working
- [ ] Backups configured
- [ ] Monitoring set up
- [ ] DNS configured correctly
- [ ] Firewall rules applied
