<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Template;
use Illuminate\Http\JsonResponse;

class TemplateController extends Controller
{
    /**
     * Display a listing of active templates.
     */
    public function index(): JsonResponse
    {
        $templates = Template::active()->ordered()->get();

        return response()->json([
            'templates' => $templates
        ]);
    }

    /**
     * Display the specified template.
     */
    public function show(Template $template): JsonResponse
    {
        if (!$template->is_active) {
            return response()->json([
                'message' => 'Template not found'
            ], 404);
        }

        return response()->json([
            'template' => $template
        ]);
    }
}
