export interface User {
  id: number;
  name: string;
  email: string;
  role: string;
  is_active: boolean;
  last_login_at: string | null;
  created_at: string;
  updated_at: string;
  subscription?: Subscription;
}

export interface Subscription {
  id: number;
  user_id: number;
  stripe_subscription_id: string | null;
  stripe_customer_id: string | null;
  plan_type: 'free' | 'premium';
  status: 'active' | 'cancelled' | 'expired';
  amount: number | null;
  current_period_start: string | null;
  current_period_end: string | null;
  cancelled_at: string | null;
  cv_limit: number;
  created_at: string;
  updated_at: string;
}

export interface Template {
  id: number;
  name: string;
  display_name: string;
  description: string | null;
  preview_image: string | null;
  styles: Record<string, any> | null;
  is_active: boolean;
  sort_order: number;
  created_at: string;
  updated_at: string;
}

export interface PersonalInfo {
  name: string;
  email: string;
  phone?: string;
  address?: string;
  summary?: string;
}

export interface Education {
  degree: string;
  school: string;
  start_date: string;
  end_date: string;
  description?: string;
}

export interface Experience {
  position: string;
  company: string;
  start_date: string;
  end_date: string;
  description?: string;
}

export interface Skill {
  name: string;
  level?: string;
}

export interface Language {
  name: string;
  level?: string;
}

export interface CV {
  id: number;
  user_id: number;
  template_id: number;
  title: string;
  personal_info: PersonalInfo;
  education: Education[] | null;
  experience: Experience[] | null;
  skills: Skill[] | null;
  languages: Language[] | null;
  additional_sections: Record<string, any> | null;
  is_draft: boolean;
  last_edited_at: string | null;
  created_at: string;
  updated_at: string;
  template?: Template;
}

export interface AuthResponse {
  message: string;
  user: User;
  access_token: string;
  token_type: string;
}

export interface ApiResponse<T> {
  message?: string;
  data?: T;
  errors?: Record<string, string[]>;
}

export interface SubscriptionPlan {
  id: string;
  name: string;
  price: number;
  cv_limit: number;
  features: string[];
}
