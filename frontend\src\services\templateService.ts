import { apiClient } from './api';
import { ENDPOINTS } from '../config/api';
import { Template } from '../types';

class TemplateService {
  async getTemplates(): Promise<{ templates: Template[] }> {
    return await apiClient.get<{ templates: Template[] }>(ENDPOINTS.TEMPLATES);
  }

  async getTemplate(id: number): Promise<{ template: Template }> {
    return await apiClient.get<{ template: Template }>(ENDPOINTS.TEMPLATE(id));
  }
}

export const templateService = new TemplateService();
