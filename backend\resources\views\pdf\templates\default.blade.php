<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $personal_info['name'] ?? 'CV' }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 15px;
            border-bottom: 1px solid #ccc;
        }
        
        .name {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .contact-info {
            font-size: 11px;
            color: #666;
        }
        
        .section {
            margin-bottom: 20px;
        }
        
        .section-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 8px;
            text-transform: uppercase;
        }
        
        .item {
            margin-bottom: 10px;
        }
        
        .item-title {
            font-weight: bold;
        }
        
        .item-subtitle {
            font-style: italic;
            color: #666;
        }
        
        .item-date {
            color: #666;
            font-size: 10px;
        }
        
        .skills-list {
            list-style: none;
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .skill-item {
            background: #f0f0f0;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <h1 class="name">{{ $personal_info['name'] ?? 'Your Name' }}</h1>
        <div class="contact-info">
            @if(isset($personal_info['email']))
                {{ $personal_info['email'] }}
            @endif
            @if(isset($personal_info['phone']))
                | {{ $personal_info['phone'] }}
            @endif
            @if(isset($personal_info['address']))
                | {{ $personal_info['address'] }}
            @endif
        </div>
    </div>

    <!-- Professional Summary -->
    @if(isset($personal_info['summary']))
    <div class="section">
        <h2 class="section-title">Summary</h2>
        <p>{{ $personal_info['summary'] }}</p>
    </div>
    @endif

    <!-- Experience -->
    @if(!empty($experience))
    <div class="section">
        <h2 class="section-title">Experience</h2>
        @foreach($experience as $exp)
        <div class="item">
            <div class="item-title">{{ $exp['position'] ?? 'Position' }}</div>
            <div class="item-subtitle">{{ $exp['company'] ?? 'Company' }}</div>
            <div class="item-date">
                {{ $exp['start_date'] ?? 'Start' }} - {{ $exp['end_date'] ?? 'End' }}
            </div>
            @if(isset($exp['description']))
            <p>{{ $exp['description'] }}</p>
            @endif
        </div>
        @endforeach
    </div>
    @endif

    <!-- Education -->
    @if(!empty($education))
    <div class="section">
        <h2 class="section-title">Education</h2>
        @foreach($education as $edu)
        <div class="item">
            <div class="item-title">{{ $edu['degree'] ?? 'Degree' }}</div>
            <div class="item-subtitle">{{ $edu['school'] ?? 'School' }}</div>
            <div class="item-date">
                {{ $edu['start_date'] ?? 'Start' }} - {{ $edu['end_date'] ?? 'End' }}
            </div>
        </div>
        @endforeach
    </div>
    @endif

    <!-- Skills -->
    @if(!empty($skills))
    <div class="section">
        <h2 class="section-title">Skills</h2>
        <ul class="skills-list">
            @foreach($skills as $skill)
            <li class="skill-item">{{ is_array($skill) ? $skill['name'] : $skill }}</li>
            @endforeach
        </ul>
    </div>
    @endif

    <!-- Languages -->
    @if(!empty($languages))
    <div class="section">
        <h2 class="section-title">Languages</h2>
        @foreach($languages as $lang)
        <div class="item">
            {{ is_array($lang) ? $lang['name'] : $lang }}
            @if(is_array($lang) && isset($lang['level']))
            - {{ $lang['level'] }}
            @endif
        </div>
        @endforeach
    </div>
    @endif
</body>
</html>
