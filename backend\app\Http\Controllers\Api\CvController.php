<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Cv;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class CvController extends Controller
{
    /**
     * Display a listing of the user's CVs.
     */
    public function index(Request $request): JsonResponse
    {
        $cvs = $request->user()->cvs()->with('template')->latest()->get();

        return response()->json([
            'cvs' => $cvs
        ]);
    }

    /**
     * Store a newly created CV in storage.
     */
    public function store(Request $request): JsonResponse
    {
        // Check if user can create more CVs
        if (!$request->user()->canCreateCv()) {
            return response()->json([
                'message' => 'CV limit reached. Please upgrade your subscription.'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'template_id' => 'required|exists:templates,id',
            'personal_info' => 'required|array',
            'personal_info.name' => 'required|string|max:255',
            'personal_info.email' => 'required|email|max:255',
            'education' => 'nullable|array',
            'experience' => 'nullable|array',
            'skills' => 'nullable|array',
            'languages' => 'nullable|array',
            'additional_sections' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $cv = $request->user()->cvs()->create([
            'title' => $request->title,
            'template_id' => $request->template_id,
            'personal_info' => $request->personal_info,
            'education' => $request->education,
            'experience' => $request->experience,
            'skills' => $request->skills,
            'languages' => $request->languages,
            'additional_sections' => $request->additional_sections,
            'is_draft' => $request->boolean('is_draft', true),
            'last_edited_at' => now(),
        ]);

        return response()->json([
            'message' => 'CV created successfully',
            'cv' => $cv->load('template')
        ], 201);
    }

    /**
     * Display the specified CV.
     */
    public function show(Request $request, Cv $cv): JsonResponse
    {
        // Ensure user owns this CV
        if ($cv->user_id !== $request->user()->id) {
            return response()->json([
                'message' => 'Unauthorized'
            ], 403);
        }

        return response()->json([
            'cv' => $cv->load('template')
        ]);
    }

    /**
     * Update the specified CV in storage.
     */
    public function update(Request $request, Cv $cv): JsonResponse
    {
        // Ensure user owns this CV
        if ($cv->user_id !== $request->user()->id) {
            return response()->json([
                'message' => 'Unauthorized'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'title' => 'sometimes|required|string|max:255',
            'template_id' => 'sometimes|required|exists:templates,id',
            'personal_info' => 'sometimes|required|array',
            'education' => 'nullable|array',
            'experience' => 'nullable|array',
            'skills' => 'nullable|array',
            'languages' => 'nullable|array',
            'additional_sections' => 'nullable|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $cv->update(array_merge(
            $request->only([
                'title', 'template_id', 'personal_info', 'education',
                'experience', 'skills', 'languages', 'additional_sections'
            ]),
            [
                'is_draft' => $request->boolean('is_draft', $cv->is_draft),
                'last_edited_at' => now(),
            ]
        ));

        return response()->json([
            'message' => 'CV updated successfully',
            'cv' => $cv->load('template')
        ]);
    }

    /**
     * Remove the specified CV from storage.
     */
    public function destroy(Request $request, Cv $cv): JsonResponse
    {
        // Ensure user owns this CV
        if ($cv->user_id !== $request->user()->id) {
            return response()->json([
                'message' => 'Unauthorized'
            ], 403);
        }

        $cv->delete();

        return response()->json([
            'message' => 'CV deleted successfully'
        ]);
    }
}
